import { NextResponse } from 'next/server'

export async function POST() {
  try {
    // Forward sync request to CMS
    const cmsResponse = await fetch('http://localhost:3001/api/sync/challenges', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
    })

    if (!cmsResponse.ok) {
      throw new Error('Failed to sync challenges')
    }

    const cmsData = await cmsResponse.json()

    return NextResponse.json({
      success: true,
      syncedCount: cmsData.stats?.synced || 0,
      message: 'Challenges synced successfully'
    })
  } catch (error) {
    console.error('Error syncing challenges:', error)
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to sync challenges' 
      },
      { status: 500 }
    )
  }
}
