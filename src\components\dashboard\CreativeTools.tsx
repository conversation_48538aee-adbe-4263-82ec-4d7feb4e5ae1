import React from "react";
import LearnTip from "../shared/LearnTip";
// Removed unused imports
import {
    CreativeToolsHeader,
    CreativeChallenge,
    CreativeToolsGrid,
} from "./creative";

interface CreativeToolsProps {
    showChallenges: boolean;
    setShowChallenges: React.Dispatch<React.SetStateAction<boolean>>;
}

const CreativeTools = ({ showChallenges, setShowChallenges }: CreativeToolsProps) => {
    const safeMode = true;

    return (
        <div className="bg-white p-6 rounded-xl shadow-sm">
            <CreativeToolsHeader
                showChallenges={showChallenges}
                setShowChallenges={setShowChallenges}
            />

            {safeMode && (
                <div className="mb-6">
                    <LearnTip
                        tip="LittleSpark Safe AI Mode is on! This means all AI responses are filtered for age-appropriate content and learning opportunities."
                        subject="Safe AI Mode"
                        type="learning"
                    />
                </div>
            )}

            {showChallenges && <CreativeChallenge />}

            <CreativeToolsGrid />
        </div>
    );
};

export default CreativeTools;
