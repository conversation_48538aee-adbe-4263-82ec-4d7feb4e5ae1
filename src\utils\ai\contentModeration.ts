import { toast } from 'sonner';

// Expanded list – profanity, explicit, violence, weapons, and drugs keywords
const PROFANITY_LIST = [
  // Mild insults / general profanity
  'idiot', 'stupid', 'dumb', 'moron', 'loser', 'jerk', 'retard', 'lame', 'trash',

  // Profanity / swearing (incl. variants)
  'shit', 'sh1t', 'sht', 'fuck', 'fuk', 'f*ck', 'fking', 'fkn', 'fml',
  'bitch', 'b!tch', 'biatch', 'b1tch', 'bastard', 'asshole', 'a-hole', 'arsehole',
  'dick', 'd1ck', 'cunt', 'c*nt', 'piss', 'cock', 'prick', 'slut', 'whore', 'hoe',

  // Sexual content and slangs
  'porn', 'pron', 'p0rn', 'p*rn', 'nude', 'naked', 'boobs', 'tits', 'nipples', 'penis', 'vagina',
  'dildo', 'cum', 'orgasm', 'anal', 'blowjob', 'bj', 'handjob', 'hentai', 'xxx', '69', 'sexting', 'fap',

  // Hate speech / slurs (examples, not exhaustive – customize as needed)
  'slur1', 'slur2', 'slur3', // placeholder
  'nigger', 'nigga', 'chink', 'kike', 'tranny', 'faggot', 'dyke', 'terrorist', 'paki',
  'raghead', 'retard', 'cripple', 'fatass', 'coon', 'wetback', 'gook', 'homo', 'twink',

  // Violence, weapons, and self-harm
  'murder', 'stab', 'bomb', 'suicide', 'selfharm', 'self-harm',
  'hang', 'cut', 'knife', 'bleed', 'massacre', 'terror',

  // Drug and substance abuse
  'cocaine', 'heroin', 'meth', 'marijuana', 'hash', 'lsd', 'ecstasy',
  'mdma', 'crack', 'dope', 'joint', 'bong', 'sniff', 'snort', 'inject', 'stoned',

  // Other mature/inappropriate content
  'onlyfans', 'nsfw', 'escort', 'camgirl', 'sugar daddy', 'sugarbaby', 'stripper', 'prostitute',
  'incest', 'pedo', 'zoophile', 'rape', 'molest', 'abuse', 'gore', 'snuff', 'necrophilia',

  // Leetspeak or obfuscated forms
  's3x', 'f4p', 'f@p', 'h0rny', 'd1ld0', 'n@ked', 'c0ck', 'c0nd0m', 'ejacul8', 'cl1t', 'clit',
  'v@g', 'n1gga', 'r4pe', 'k1ll', 'sux', 's3lfh4rm', 'fisting', 's3xt', 'anal3', 'nips'
];


// Additional sensitive regex patterns (e.g., "how to buy a gun")
const SENSITIVE_PATTERNS: RegExp[] = [
  // Guns, weapons, bombs
  /how to (buy|get|build|make|use).*?(gun|weapon|rifle|pistol|firearm|shotgun)/i,
  /(3d print|print).*?(gun|weapon|firearm|pistol)/i,
  /how to (make|build).*?(bomb|explosive|grenade|molotov)/i,
  /bomb (recipe|manual|making|assembly)/i,
  /how to make.*?(nuke|rocket|missile)/i,

  // Suicide and self-harm
  /how to (commit|do|attempt).*?(suicide|self harm|self-harm|kill myself)/i,
  /(i want to|thinking about|planning to).*(die|kill myself|end it all|end my life)/i,
  /(ways to|methods to).*(self harm|kill myself|suicide|bleed out|hang myself)/i,

  // Drug and substance-related
  /how to (buy|get|make|cook|inject|snort|smoke).*?(drugs|heroin|meth|cocaine|lsd|weed|marijuana|crack)/i,
  /how to (grow|harvest|sell).*?(weed|marijuana|pot|hash)/i,
  /how to (get|order).*?(xanax|opioids|adderall|vicodin|molly|ecstasy|mdma)/i,

  // Sexual exploitation or inappropriate behavior
  /how to (meet|contact|chat with|find).*?(escort|prostitute|camgirl|sugar baby|onlyfans)/i,
  /how to (hide|erase|delete).*?(porn|nudes|explicit content)/i,
  /how to (blackmail|threaten).*?(girl|boy|someone) using (nudes|photos|videos)/i,

  // Violence, murder, or threats
  /how to (kill|murder|stab|shoot|hurt).*?(someone|a person|my enemy)/i,
  /ways to (torture|punish|attack|stab)/i,
  /how to (poison|strangle|drown)/i,

  // Cyber abuse / stalking / hacking
  /how to (hack|track|spy on|monitor).*?(phone|whatsapp|instagram|account|girl)/i,
  /how to (dox|doxx|leak info|leak photos)/i,
  /how to (create|spread) a (rumor|fake account|nude leak)/i,
];


interface ModerationResult {
  isAppropriate: boolean;
  reason?: string;
  blocked?: boolean;
}

// Track user strikes in-memory (keyed by userId or IP). In production, move this to Redis / DB.
interface ViolationState {
  count: number;
  blockedUntil: number; // epoch ms
}

const violationTracker = new Map<string, ViolationState>();

const STRIKE_LIMIT = 3;
const BLOCK_DURATION_MS = 5 * 60 * 1000; // 5 minutes

const containsProhibited = (text: string): boolean => {
  const lower = text.toLowerCase();
  if (PROFANITY_LIST.some((word) => lower.includes(word))) return true;
  return SENSITIVE_PATTERNS.some((re) => re.test(text));
};

const incrementViolation = (userKey: string): ViolationState => {
  const state = violationTracker.get(userKey) || { count: 0, blockedUntil: 0 };
  // If currently blocked, keep same blockedUntil
  if (state.blockedUntil && state.blockedUntil > Date.now()) {
    return state;
  }
  state.count += 1;
  if (state.count >= STRIKE_LIMIT) {
    state.blockedUntil = Date.now() + BLOCK_DURATION_MS;
    state.count = 0; // reset after block
  }
  violationTracker.set(userKey, state);
  return state;
};

export const moderateContent = async (
  text: string,
  _type: 'text' | 'prompt',
  userKey?: string
): Promise<ModerationResult> => {
  // Content safety active in both development and production
  // if (process.env.NODE_ENV === 'development') {
  //   console.log('[DEV] Skipping content moderation for:', text.substring(0, 100));
  //   return { isAppropriate: true };
  // }

  // Fast path – nothing suspicious
  if (!containsProhibited(text)) {
    // Reset strikes on good behaviour
    if (userKey && violationTracker.has(userKey)) {
      const state = violationTracker.get(userKey)!;
      state.count = 0;
      violationTracker.set(userKey, state);
    }
    return { isAppropriate: true };
  }

  // If we reach here, content is NOT appropriate
  if (!userKey) {
    return {
      isAppropriate: false,
      reason: 'Inappropriate content detected.',
    };
  }

  // Handle strike logic when userKey is provided
  const state = incrementViolation(userKey);

  if (state.blockedUntil && state.blockedUntil > Date.now()) {
    return {
      isAppropriate: false,
      blocked: true,
      reason: `Too many unsafe requests. You are blocked until ${new Date(
        state.blockedUntil
      ).toLocaleTimeString()}.`,
    };
  }

  return {
    isAppropriate: false,
    reason: `Inappropriate content detected. Strike ${state.count}/${STRIKE_LIMIT}.`,
  };
};

// Helper to surface toast on client side (optional)
export const handleInappropriateContent = (type: string, reason?: string) => {
  toast.error(`Unable to generate ${type}`, {
    description:
      reason || 'The requested content may not be appropriate. Please try different wording.',
  });
};
