/**
 * Test script for the sync system
 * Run this script to validate sync functionality
 * 
 * Usage: node test-sync-system.js
 */

const CMS_BASE_URL = process.env.CMS_BASE_URL || 'http://localhost:3001';
const MAIN_APP_BASE_URL = process.env.MAIN_APP_BASE_URL || 'http://localhost:3000';
const CMS_SYNC_TOKEN = process.env.CMS_SYNC_TOKEN || 'test-sync-token';

// Test configuration
const TEST_CONFIG = {
  cmsAdminToken: 'your-cms-admin-token-here', // Replace with actual admin token
  testTimeout: 30000, // 30 seconds
};

class SyncSystemTester {
  constructor() {
    this.results = {
      passed: 0,
      failed: 0,
      tests: []
    };
  }

  async runTest(name, testFn) {
    console.log(`\n🧪 Testing: ${name}`);
    try {
      await testFn();
      console.log(`✅ PASSED: ${name}`);
      this.results.passed++;
      this.results.tests.push({ name, status: 'PASSED' });
    } catch (error) {
      console.log(`❌ FAILED: ${name}`);
      console.log(`   Error: ${error.message}`);
      this.results.failed++;
      this.results.tests.push({ name, status: 'FAILED', error: error.message });
    }
  }

  async testCMSAuthVerification() {
    const response = await fetch(`${CMS_BASE_URL}/api/auth/verify`, {
      headers: {
        'Authorization': `Bearer ${TEST_CONFIG.cmsAdminToken}`,
      },
    });

    if (!response.ok) {
      throw new Error(`Auth verification failed: ${response.status}`);
    }

    const data = await response.json();
    if (!data.success || !data.authenticated) {
      throw new Error('Authentication verification returned invalid response');
    }

    if (!data.permissions?.isAdmin) {
      throw new Error('User does not have admin permissions');
    }

    console.log(`   ✓ User authenticated as admin: ${data.user.email}`);
  }

  async testCMSChallengesSyncStatus() {
    const response = await fetch(`${CMS_BASE_URL}/api/sync/challenges`, {
      headers: {
        'Authorization': `Bearer ${TEST_CONFIG.cmsAdminToken}`,
      },
    });

    if (!response.ok) {
      throw new Error(`Challenge sync status failed: ${response.status}`);
    }

    const data = await response.json();
    if (!data.success) {
      throw new Error('Challenge sync status returned error');
    }

    console.log(`   ✓ CMS Challenges: ${data.stats.cmsChallenge}`);
    console.log(`   ✓ Synced to Main App: ${data.stats.mainAppCmsChallenge}`);
  }

  async testCMSUsersSyncStatus() {
    const response = await fetch(`${CMS_BASE_URL}/api/sync/users`, {
      headers: {
        'Authorization': `Bearer ${TEST_CONFIG.cmsAdminToken}`,
      },
    });

    if (!response.ok) {
      throw new Error(`User sync status failed: ${response.status}`);
    }

    const data = await response.json();
    if (!data.success) {
      throw new Error('User sync status returned error');
    }

    console.log(`   ✓ CMS Users: ${data.stats.cmsUsers}`);
    console.log(`   ✓ Synced to Main App: ${data.stats.mainAppCmsUsers}`);
  }

  async testMainAppSyncEndpoints() {
    // Test challenge sync status
    const challengeResponse = await fetch(`${MAIN_APP_BASE_URL}/api/challenges/sync-status`, {
      headers: {
        'Authorization': `Bearer ${CMS_SYNC_TOKEN}`,
      },
    });

    if (!challengeResponse.ok) {
      throw new Error(`Main app challenge sync status failed: ${challengeResponse.status}`);
    }

    const challengeData = await challengeResponse.json();
    if (!challengeData.success) {
      throw new Error('Main app challenge sync status returned error');
    }

    console.log(`   ✓ Main App CMS Challenges: ${challengeData.stats.cmsChallenge}`);

    // Test user sync status
    const userResponse = await fetch(`${MAIN_APP_BASE_URL}/api/challenges/sync-status`, {
      headers: {
        'Authorization': `Bearer ${CMS_SYNC_TOKEN}`,
      },
    });

    if (!userResponse.ok) {
      throw new Error(`Main app user sync status failed: ${userResponse.status}`);
    }

    const userData = await userResponse.json();
    if (!userData.success) {
      throw new Error('Main app user sync status returned error');
    }

    console.log(`   ✓ Main App Total Challenges: ${userData.stats.totalChallenge}`);
  }

  async testUserCreatedChallengesEndpoint() {
    const response = await fetch(`${MAIN_APP_BASE_URL}/api/challenges/user-created`, {
      headers: {
        'Authorization': `Bearer ${CMS_SYNC_TOKEN}`,
      },
    });

    if (!response.ok) {
      throw new Error(`User-created challenges endpoint failed: ${response.status}`);
    }

    const data = await response.json();
    if (!data.success) {
      throw new Error('User-created challenges endpoint returned error');
    }

    console.log(`   ✓ User-created challenges available: ${data.total}`);
  }

  async testUnauthorizedAccess() {
    // Test without token
    const response1 = await fetch(`${CMS_BASE_URL}/api/sync/challenges`);
    if (response1.ok) {
      throw new Error('Unauthorized request should have been rejected');
    }

    // Test with invalid token
    const response2 = await fetch(`${CMS_BASE_URL}/api/sync/challenges`, {
      headers: {
        'Authorization': 'Bearer invalid-token',
      },
    });
    if (response2.ok) {
      throw new Error('Invalid token request should have been rejected');
    }

    console.log(`   ✓ Unauthorized access properly blocked`);
  }

  async testSyncTokenValidation() {
    // Test main app endpoint with wrong token
    const response = await fetch(`${MAIN_APP_BASE_URL}/api/challenges/sync-status`, {
      headers: {
        'Authorization': 'Bearer wrong-token',
      },
    });

    if (response.ok) {
      throw new Error('Wrong sync token should have been rejected');
    }

    console.log(`   ✓ Sync token validation working`);
  }

  async runAllTests() {
    console.log('🚀 Starting Sync System Tests');
    console.log(`📍 CMS URL: ${CMS_BASE_URL}`);
    console.log(`📍 Main App URL: ${MAIN_APP_BASE_URL}`);
    console.log('=' .repeat(50));

    // Authentication and permissions tests
    await this.runTest('CMS Admin Authentication', () => this.testCMSAuthVerification());
    await this.runTest('Unauthorized Access Prevention', () => this.testUnauthorizedAccess());
    await this.runTest('Sync Token Validation', () => this.testSyncTokenValidation());

    // Sync status tests
    await this.runTest('CMS Challenges Sync Status', () => this.testCMSChallengesSyncStatus());
    await this.runTest('CMS Users Sync Status', () => this.testCMSUsersSyncStatus());
    await this.runTest('Main App Sync Endpoints', () => this.testMainAppSyncEndpoints());
    await this.runTest('User-Created Challenges Endpoint', () => this.testUserCreatedChallengesEndpoint());

    // Print results
    console.log('\n' + '=' .repeat(50));
    console.log('📊 Test Results Summary');
    console.log('=' .repeat(50));
    console.log(`✅ Passed: ${this.results.passed}`);
    console.log(`❌ Failed: ${this.results.failed}`);
    console.log(`📈 Success Rate: ${Math.round((this.results.passed / (this.results.passed + this.results.failed)) * 100)}%`);

    if (this.results.failed > 0) {
      console.log('\n❌ Failed Tests:');
      this.results.tests
        .filter(test => test.status === 'FAILED')
        .forEach(test => {
          console.log(`   • ${test.name}: ${test.error}`);
        });
    }

    console.log('\n🎯 Next Steps:');
    if (this.results.failed === 0) {
      console.log('   ✅ All tests passed! Sync system is ready for use.');
      console.log('   📝 Update TEST_CONFIG.cmsAdminToken with a real admin token for full testing.');
    } else {
      console.log('   🔧 Fix the failed tests before deploying the sync system.');
      console.log('   📚 Check the SYNC_SYSTEM_README.md for troubleshooting guide.');
    }

    return this.results.failed === 0;
  }
}

// Run tests if this script is executed directly
if (require.main === module) {
  const tester = new SyncSystemTester();
  
  tester.runAllTests()
    .then(success => {
      process.exit(success ? 0 : 1);
    })
    .catch(error => {
      console.error('💥 Test runner failed:', error);
      process.exit(1);
    });
}

module.exports = SyncSystemTester;
