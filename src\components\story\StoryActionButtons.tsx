"use client";

import React, { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Save, Download, MessageCircle, RefreshCw, Edit2, Loader2 } from "lucide-react";
import { toast } from "sonner";
import { useRouter } from "next/navigation";
import { generateStoryOption } from "@/utils/ai/storyService";
import { useStory } from "./StoryContext";
import { saveUserContent } from "@/utils/contentStorage";

const StoryActionButtons = () => {
    const router = useRouter();
    const {
        title,
        content,
        setContent,
        isGenerating,
        setIsGenerating,
        // learningMode,
        readerAge,
        lastFormat,
        storyStage,
        storyTheme,
        storyGenre,
        showOptions,
        setShowOptions,
        storyOptions,
        setStoryOptions,
    } = useStory();

    const [isSaving, setIsSaving] = useState(false);

    // const handleGenerateIdea = async () => {
    //     setIsGenerating(true);
    //     try {
    //         const idea = await generateStoryIdea(
    //             readerAge,
    //             lastFormat,
    //             title,
    //             storyTheme,
    //             storyGenre
    //         );
    //         setContent(idea);

    //         if (learningMode) {
    //             toast.success(
    //                 "This story idea introduces key narrative elements: character, setting, and conflict!"
    //             );
    //         } else {
    //             toast.success("Story idea generated!");
    //         }
    //     } catch (error) {
    //         console.error("Error generating story idea:", error);
    //         toast.error("Failed to generate a story idea. Please try again.");
    //     } finally {
    //         setIsGenerating(false);
    //     }
    // };

    const handleContinueStory = async () => {
        if (!content?.trim()) {
            toast.error("Please write some story content first before continuing");
            return;
        }
        setIsGenerating(true);
        try {
            const options = await Promise.all([
                generateStoryOption(content, storyStage, readerAge, lastFormat || "adventure", title, storyTheme, storyGenre),
                generateStoryOption(content, storyStage, readerAge, lastFormat || "adventure", title, storyTheme, storyGenre),
                generateStoryOption(content, storyStage, readerAge, lastFormat || "adventure", title, storyTheme, storyGenre)
            ]);
            setStoryOptions(options);
            setShowOptions(true);
        } catch (error) {
            console.error("Error generating story options:", error);
            toast.error("Failed to generate story options. Please try again.");
        } finally {
            setIsGenerating(false);
        }
    };

    const handleSaveStory = async () => {
        if (!title) {
            toast.error("Please add a title to your story");
            return;
        }

        if (content.length < 20) {
            toast.error("Your story is too short. Keep writing!");
            return;
        }

        setIsSaving(true);
        try {
            // Check if there's an active challenge
        let challengeId: string | undefined;
        try {
            const currentChallenge = localStorage.getItem('currentChallenge');
            if (currentChallenge) {
                const challenge = JSON.parse(currentChallenge);
                // Only link if the challenge type matches the content type
                if (challenge.type === 'story') {
                    challengeId = challenge.id;
                }
            }
        } catch (error) {
            console.warn('Failed to parse current challenge from localStorage:', error);
        }

        // Save story to user's content
        const savedStory = await saveUserContent({
            type: "story",
            title,
            content_metadata: {
                fullContent: content,
                preview: content.substring(0, 60) + "...",
                storyTheme,
                storyGenre,
                storyStage,
            },
            preview_url: null,
            challenge_id: challengeId
        });

        if (savedStory) {
            toast.success("Story saved to your portfolio!");

            // Trigger event to update dashboard counters
            window.dispatchEvent(new CustomEvent('contentSaved', {
                detail: {
                    type: 'story',
                    title: title
                }
            }));

            // Redirect to projects section after successful save
            setTimeout(() => {
                router.push("/my-projects");
            }, 1500);
        } else {
            toast.error("Failed to save your story. Please try again.");
        }
        } catch (error) {
            console.error("Error saving story:", error);
            toast.error("Failed to save your story. Please try again.");
        } finally {
            setIsSaving(false);
        }
    };

    const handleDownload = () => {
        // Create a blob with the story content
        const blob = new Blob([`${title}\n\n${content}`], {
            type: "text/plain",
        });

        // Create a URL for the blob
        const url = URL.createObjectURL(blob);

        // Create a link element
        const link = document.createElement("a");
        link.href = url;
        link.download = `${title || "story"}.txt`;

        // Append to the body, click and remove
        document.body.appendChild(link);
        link.click();

        // Clean up
        document.body.removeChild(link);
        URL.revokeObjectURL(url);

        toast.success("Story downloaded successfully!");
    };

    const handleSelectOption = (option: string) => {
        setContent(content + "\n\n" + option);
        setShowOptions(false);
        toast.success("Added to your story!");
    };

    return (
        <>
            <div className="flex flex-col items-center gap-4 mb-6">
                <Button
                    onClick={handleContinueStory}
                    disabled={isGenerating}
                    className="w-full max-w-xl bg-gradient-to-r from-[#4158D0] via-[#C850C0] to-[#FFCC70] hover:opacity-90 text-white font-medium shadow-sm rounded-full transition-all min-h-[44px] text-sm sm:text-base"
                >
                    <MessageCircle className="h-4 w-4 mr-2" />
                    <span className="sm:hidden">Continue Story</span>
                    <span className="hidden sm:inline">Help Me Continue My Story</span>
                </Button>

                <div className="flex flex-col sm:flex-row justify-center gap-3 w-full sm:w-auto">
                    <Button
                        onClick={handleSaveStory}
                        disabled={isSaving}
                        className="bg-gradient-to-r from-[#FF6B6B] to-[#FF9999] hover:opacity-90 text-white font-medium px-4 sm:px-6 rounded-full transition-all min-h-[44px] text-sm sm:text-base w-full sm:w-auto"
                    >
                        {isSaving ? (
                            <>
                                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                                <span className="sm:hidden">Saving...</span>
                                <span className="hidden sm:inline">Saving...</span>
                            </>
                        ) : (
                            <>
                                <Save className="h-4 w-4 mr-2" />
                                <span className="sm:hidden">Save</span>
                                <span className="hidden sm:inline">Save to Portfolio</span>
                            </>
                        )}
                    </Button>
                    
                    <Button
                        variant="outline"
                        className="border-2 border-[#5B86E5] text-[#5B86E5] hover:bg-gradient-to-r hover:from-[#36D1DC] hover:to-[#5B86E5] hover:text-white font-medium px-4 sm:px-6 rounded-full transition-all min-h-[44px] text-sm sm:text-base w-full sm:w-auto"
                        onClick={handleDownload}
                    >
                        <Download className="h-4 w-4 mr-2" /> Download
                    </Button>
                </div>
            </div>
            {showOptions && (
                <div className="w-full max-w-xl mx-auto mt-6">
                    <h3 className="text-lg sm:text-xl font-bold text-center mb-4">
                        <span className="sm:hidden">What happens next?</span>
                        <span className="hidden sm:inline">Choose what happens next:</span>
                    </h3>
                    <div className="space-y-3">
                        {storyOptions.map((option, idx) => (
                            <div
                                key={idx}
                                onClick={() => handleSelectOption(option)}
                                className="p-3 sm:p-4 border-2 border-[#5B86E5] bg-gradient-to-r from-[#EEF2FF] to-[#E6F0FF] hover:from-[#E6F0FF] hover:to-[#D9E8FF] rounded-xl cursor-pointer text-center text-gray-700 transition-all shadow-sm text-sm sm:text-base min-h-[44px] flex items-center justify-center"
                            >
                                {option}
                            </div>
                        ))}
                    </div>
                    <div className="flex flex-col sm:flex-row justify-center gap-3 sm:gap-4 mt-6">
                        <Button 
                            variant="outline" 
                            onClick={() => setShowOptions(false)} 
                            className="bg-white border-2 border-[#FF6B6B] text-[#FF6B6B] hover:bg-gradient-to-r hover:from-[#FF6B6B] hover:to-[#FF9999] hover:text-white font-medium px-4 sm:px-8 py-2 rounded-full transition-all min-h-[44px] text-sm sm:text-base w-full sm:w-auto"
                        >
                            <Edit2 className="h-4 w-4 mr-2" />
                            <span className="sm:hidden">Write Own</span>
                            <span className="hidden sm:inline">Write My Own</span>
                        </Button>
                        <Button 
                            variant="outline" 
                            onClick={handleContinueStory} 
                            className="bg-white border-2 border-[#36D1DC] text-[#36D1DC] hover:bg-gradient-to-r hover:from-[#36D1DC] hover:to-[#5B86E5] hover:text-white font-medium px-4 sm:px-8 py-2 rounded-full transition-all min-h-[44px] text-sm sm:text-base w-full sm:w-auto"
                        >
                            <RefreshCw className="h-4 w-4 mr-2" />
                            <span className="sm:hidden">New Ideas</span>
                            <span className="hidden sm:inline">New Options</span>
                        </Button>
                    </div>
                </div>
            )}
        </>
    );
};

export default StoryActionButtons;
