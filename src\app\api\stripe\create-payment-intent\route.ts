import { NextRequest, NextResponse } from 'next/server';
import { stripe, STRIPE_PLANS, getPlanDisplayName } from '@/lib/stripe';
import { prisma } from '@/lib/prisma';

export async function POST(request: NextRequest) {
  try {
    if (!stripe) {
      return NextResponse.json({ error: 'Stripe not configured' }, { status: 500 });
    }

    const { planId, email, customerName, userId, promoCode } = await request.json();

    if (!planId || !email) {
      return NextResponse.json({ error: 'Missing required fields' }, { status: 400 });
    }

    const plan = STRIPE_PLANS[planId as keyof typeof STRIPE_PLANS];
    if (!plan) {
      return NextResponse.json({ error: 'Invalid plan ID' }, { status: 400 });
    }

    // Handle promo codes
    let discountAmount = 0;
    let finalAmount: number = plan.amount;
    let isZeroCostPayment = false;

    if (promoCode) {
      if (promoCode === 'ALPHA100') {
        discountAmount = plan.amount; // 100% discount
        finalAmount = 0; // Zero cost payment
        isZeroCostPayment = true;
      } else {
        return NextResponse.json({ error: 'Invalid promo code' }, { status: 400 });
      }
    }

    // Check if Stripe is properly configured for this plan
    if (!plan.priceId) {
      // For development/testing, create a simple payment intent
      if (process.env.NODE_ENV === 'development') {
        const paymentIntent = await stripe.paymentIntents.create({
          amount: finalAmount,
          currency: 'usd',
          metadata: {
            planId: planId,
            email: email,
            customerName: customerName || '',
            userId: userId || '',
            testMode: 'true',
            promoCode: promoCode || '',
            discountAmount: discountAmount.toString(),
            originalAmount: plan.amount.toString()
          }
        });

        return NextResponse.json({
          clientSecret: paymentIntent.client_secret,
          testMode: true,
          discountAmount,
          finalAmount,
          originalAmount: plan.amount
        });
      }

      return NextResponse.json(
        { error: `Price ID not configured for plan: ${planId}. Please set STRIPE_${planId.toUpperCase().replace('-', '_')}_PRICE_ID in your environment variables.` },
        { status: 500 }
      );
    }

    // Find or create profile to check trial history
    let profile = await prisma.profile.findUnique({
      where: { email }
    });

    if (!profile && userId) {
      // If we have a userId but no profile by email, try to find by userId
      profile = await prisma.profile.findUnique({
        where: { id: userId }
      });
    }

    // Create profile if it doesn't exist
    if (!profile) {
      const profileId = userId || crypto.randomUUID();
      profile = await prisma.profile.create({
        data: {
          id: profileId,
          email,
          full_name: customerName,

          created_at: new Date(),
          updated_at: new Date()
        }
      });
    } else if (!profile.full_name && customerName) {
      // Update name if not set
      await prisma.profile.update({
        where: { id: profile.id },
        data: { 
          full_name: customerName,
          updated_at: new Date()
        }
      });
    }

    console.log('Direct purchase flow:', {
      email,
      profileExists: !!profile,
      planId,
      promoCode,
      isZeroCostPayment,
      finalAmount
    });

    // Create or retrieve Stripe customer
    let customer;
    const existingCustomers = await stripe.customers.list({
      email: email,
      limit: 1,
    });

    if (existingCustomers.data.length > 0) {
      customer = existingCustomers.data[0];
    } else {
      customer = await stripe.customers.create({
        email: email,
        name: customerName || undefined,
      });
    }

    // Update profile with Stripe customer ID
    if (profile && !profile.stripe_customer_id) {
      await prisma.profile.update({
        where: { id: profile.id },
        data: { stripe_customer_id: customer.id }
      });
    }

    // Handle zero-cost payment (ALPHA100 promo code)
    if (isZeroCostPayment) {
      console.log('Processing zero-cost payment with ALPHA100 promo code');

      // Directly activate subscription without payment
      await prisma.profile.update({
        where: { id: profile!.id },
        data: {
          subscription_status: 'active',
          plan_id: planId,
          plan_name: getPlanDisplayName(planId),
          billing_cycle: planId.includes('monthly') ? 'monthly' :
                        planId.includes('quarterly') ? 'quarterly' : 'annual',
          subscription_start: new Date(),
          subscription_end: new Date(Date.now() + (planId.includes('monthly') ? 30 :
                                                   planId.includes('quarterly') ? 90 : 365) * 24 * 60 * 60 * 1000),
          stripe_customer_id: customer.id,
          updated_at: new Date()
        }
      });

      // Create a payment record for tracking
      await prisma.payment.create({
        data: {
          profile_id: profile!.id,
          stripe_payment_id: `promo_${Date.now()}`,
          amount: 0,
          currency: 'usd',
          status: 'succeeded',
          plan_id: planId,
          plan_name: getPlanDisplayName(planId),
          payment_date: new Date(),
          created_at: new Date()
        }
      });

      return NextResponse.json({
        success: true,
        zeroCostPayment: true,
        promoCode: 'ALPHA100',
        planActivated: planId,
        message: 'Subscription activated with promo code!'
      });
    }

    // Create regular payment intent for paid subscriptions
    console.log('Creating payment intent for direct purchase:', {
      customer: customer.id,
      priceId: plan.priceId,
      planId,
      amount: finalAmount
    });

    const paymentIntent = await stripe.paymentIntents.create({
      amount: finalAmount,
      currency: 'usd',
      customer: customer.id,
      setup_future_usage: 'off_session',
      metadata: {
        planId: planId,
        email: email,
        customerName: customerName || '',
        userId: profile?.id || '',
        direct_subscription: 'true',
        promoCode: promoCode || '',
        discountAmount: discountAmount.toString(),
        originalAmount: plan.amount.toString()
      }
    });

    console.log('Payment intent created:', paymentIntent.id);
    console.log('Amount:', finalAmount / 100, 'USD');

    return NextResponse.json({
      clientSecret: paymentIntent.client_secret,
      paymentIntentId: paymentIntent.id,
      customerId: customer.id,
      setupIntent: false,
      requiresPaymentMethod: true,
      isTrialSubscription: false,
      amount: finalAmount / 100,
      discountAmount: discountAmount / 100,
      originalAmount: plan.amount / 100,
      promoCode: promoCode || null
    });
  } catch (error) {
    console.error('Error creating payment intent:', error);
    return NextResponse.json(
      { error: 'Failed to create payment intent' },
      { status: 500 }
    );
  }
} 