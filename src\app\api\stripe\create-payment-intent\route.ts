import { NextRequest, NextResponse } from 'next/server';
import { stripe, STRIPE_PLANS, getPlanDisplayName } from '@/lib/stripe';
import { prisma } from '@/lib/prisma';

export async function POST(request: NextRequest) {
  try {
    if (!stripe) {
      return NextResponse.json({ error: 'Stripe not configured' }, { status: 500 });
    }

    const { planId, email, customerName, userId, promoCode } = await request.json();

    if (!planId || !email) {
      return NextResponse.json({ error: 'Missing required fields' }, { status: 400 });
    }

    const plan = STRIPE_PLANS[planId as keyof typeof STRIPE_PLANS];
    if (!plan) {
      return NextResponse.json({ error: 'Invalid plan ID' }, { status: 400 });
    }

    // Handle promo codes
    let discountAmount = 0;
    let finalAmount: number = plan.amount;
    let isZeroCostPayment = false;

    if (promoCode) {
      if (promoCode === 'ALPHA100') {
        discountAmount = plan.amount; // 100% discount
        finalAmount = 0; // Zero cost payment
        isZeroCostPayment = true;
      } else {
        return NextResponse.json({ error: 'Invalid promo code' }, { status: 400 });
      }
    }

    // Check if Stripe is properly configured for this plan
    if (!plan.priceId) {
      // For development/testing, create a simple payment intent
      if (process.env.NODE_ENV === 'development') {
        const paymentIntent = await stripe.paymentIntents.create({
          amount: finalAmount,
          currency: 'usd',
          metadata: {
            planId: planId,
            email: email,
            customerName: customerName || '',
            userId: userId || '',
            testMode: 'true',
            promoCode: promoCode || '',
            discountAmount: discountAmount.toString(),
            originalAmount: plan.amount.toString()
          }
        });

        return NextResponse.json({
          clientSecret: paymentIntent.client_secret,
          testMode: true,
          discountAmount,
          finalAmount,
          originalAmount: plan.amount
        });
      }

      return NextResponse.json(
        { error: `Price ID not configured for plan: ${planId}. Please set STRIPE_${planId.toUpperCase().replace('-', '_')}_PRICE_ID in your environment variables.` },
        { status: 500 }
      );
    }

    // Find or create profile to check trial history
    let profile = await prisma.profile.findUnique({
      where: { email }
    });

    if (!profile && userId) {
      // If we have a userId but no profile by email, try to find by userId
      profile = await prisma.profile.findUnique({
        where: { id: userId }
      });
    }

    // Create profile if it doesn't exist
    if (!profile) {
      const profileId = userId || crypto.randomUUID();
      profile = await prisma.profile.create({
        data: {
          id: profileId,
          email,
          full_name: customerName,

          created_at: new Date(),
          updated_at: new Date()
        }
      });
    } else if (!profile.full_name && customerName) {
      // Update name if not set
      await prisma.profile.update({
        where: { id: profile.id },
        data: { 
          full_name: customerName,
          updated_at: new Date()
        }
      });
    }

    console.log('Direct purchase flow:', {
      email,
      profileExists: !!profile,
      planId,
      promoCode,
      isZeroCostPayment,
      finalAmount
    });

    // Create or retrieve Stripe customer
    let customer;
    const existingCustomers = await stripe.customers.list({
      email: email,
      limit: 1,
    });

    if (existingCustomers.data.length > 0) {
      customer = existingCustomers.data[0];
    } else {
      customer = await stripe.customers.create({
        email: email,
        name: customerName || undefined,
      });
    }

    // Update profile with Stripe customer ID
    if (profile && !profile.stripe_customer_id) {
      await prisma.profile.update({
        where: { id: profile.id },
        data: { stripe_customer_id: customer.id }
      });
    }

    // For all payments, create a setup intent that can handle both regular payments and promo codes
    // This allows the frontend to apply promo codes without reinitializing
    const setupIntent = await stripe.setupIntents.create({
      customer: customer.id,
      payment_method_types: ['card'],
      usage: 'off_session',
      metadata: {
        userId: profile!.id,
        planId,
        promoCode: promoCode || '',
        purpose: isZeroCostPayment ? 'promo_subscription_setup' : 'regular_subscription_setup'
      },
    });

    return NextResponse.json({
      success: true,
      clientSecret: setupIntent.client_secret,
      setupIntent: true,
      isPromoCode: isZeroCostPayment,
      promoCode: promoCode || '',
      planId,
      amount: finalAmount,
      message: isZeroCostPayment ? 'Setup payment method for future billing' : 'Setup payment method'
    });


  } catch (error) {
    console.error('Error creating payment intent:', error);
    return NextResponse.json(
      { error: 'Failed to create payment intent' },
      { status: 500 }
    );
  }
} 