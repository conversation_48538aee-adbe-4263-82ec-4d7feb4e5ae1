import React from "react";
import {
    <PERSON><PERSON><PERSON>,
    Brush,
    Music,
    Gamepad2,
    Palette,
    Video,
    Code,
    Loader2,
    FolderOpen,
} from "lucide-react";
import CreativeCard from "../CreativeCard";
import { toast } from "sonner";
import { useRouter } from "next/navigation";
import { useSubscriptionStatus } from "@/hooks/useSubscriptionStatus";

const CreativeToolsGrid = () => {
    const router = useRouter();
    const { hasActiveSubscription, isLoading, subscriptionStatus } = useSubscriptionStatus();

    const handleCardClick = (
        path: string,
        available: boolean,
        title: string,
        isLocked: boolean
    ) => {
        // Don't allow clicks while loading
        if (isLoading) {
            return;
        }

        if (isLocked) {
            // Check if user has already used their trial
            const hasUsedTrial = subscriptionStatus?.trial_used;

            if (hasUsedTrial) {
                // Trial already used - show purchase subscription message
                toast.info(
                    `${title} requires a subscription. Your trial has been used - purchase a subscription to continue!`,
                    {
                        duration: 4000,
                        action: {
                            label: "Purchase Subscription",
                            onClick: () => router.push("/checkout?plan=monthly-tier")
                        }
                    }
                );
            } else {
                // Trial available - show start trial message
                toast.info(
                    `${title} requires a subscription. Start your free trial to unlock all features!`,
                    {
                        duration: 4000,
                        action: {
                            label: "Start Free Trial",
                            onClick: () => router.push("/checkout?plan=monthly-tier")
                        }
                    }
                );
            }
            return;
        }

        if (available) {
            router.push(path);
        } else {
            toast.info(
                `${title} is coming soon! We're working hard to bring this to you.`,
                {
                    duration: 3000,
                }
            );
        }
    };

    // Show loading state while fetching subscription
    if (isLoading) {
        return (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
                {Array.from({ length: 7 }, (_, index) => (
                    <div key={index} className="bg-white rounded-xl p-6 shadow-lg border border-gray-200 animate-pulse">
                        <div className="flex items-center gap-4 mb-4">
                            <div className="w-12 h-12 bg-gray-300 rounded-lg flex items-center justify-center">
                                <Loader2 className="h-6 w-6 text-gray-500 animate-spin" />
                            </div>
                            <div className="flex-1">
                                <div className="h-5 bg-gray-300 rounded mb-2"></div>
                                <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                            </div>
                        </div>
                        <div className="h-4 bg-gray-200 rounded mb-2"></div>
                        <div className="h-4 bg-gray-200 rounded w-5/6"></div>
                    </div>
                ))}
            </div>
        );
    }

    // Determine if features should be locked (only after loading is complete)
    const shouldLockFeatures = !hasActiveSubscription && !isLoading;

    return (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
            <div
                onClick={() =>
                    handleCardClick("/create/story", true, "Interactive Story", shouldLockFeatures)
                }
            >
                <CreativeCard
                    icon={<BookOpen className="h-12 w-12 text-white" />}
                    title="Interactive Story"
                    description="Collaborate with AI to build your own exciting stories"
                    bgColor="bg-littlespark-pink"
                    available={true}
                    linkTo="/create/story"
                    isLocked={shouldLockFeatures}
                />
            </div>

            <div
                onClick={() =>
                    handleCardClick("/create/art", true, "Art Studio", shouldLockFeatures)
                }
            >
                <CreativeCard
                    icon={<Brush className="h-12 w-12 text-white" />}
                    title="Art Studio"
                    description="Make beautiful pictures from your ideas"
                    bgColor="bg-littlespark-yellow"
                    available={true}
                    linkTo="/create/art"
                    isLocked={shouldLockFeatures}
                />
            </div>

            <div
                onClick={() =>
                    handleCardClick(
                        "/create/coloring",
                        true, // was false, now true to make it available
                        "AI Coloring Pages",
                        shouldLockFeatures
                    )
                }
            >
                <CreativeCard
                    icon={<Palette className="h-12 w-12 text-white" />}
                    title="AI Coloring Pages"
                    description="Create custom coloring pages with AI or color existing ones"
                    bgColor="bg-gray-500"
                    available={true} // was false, now true
                    linkTo="/create/coloring"
                    badge="Available now" // was 'Coming Soon', now 'Available now'
                    isLocked={shouldLockFeatures}
                />
            </div>

            <div
                onClick={() =>
                    handleCardClick("/create/music", true, "Music Composer", shouldLockFeatures)
                }
            >
                <CreativeCard
                    icon={<Music className="h-12 w-12 text-white" />}
                    title="Music Composer"
                    description="Create theme songs for your stories and characters"
                    bgColor="bg-littlespark-lavender"
                    available={true}
                    linkTo="/create/music"
                    isLocked={shouldLockFeatures}
                />
            </div>

            <div
                onClick={() =>
                    handleCardClick("/create/video", true, "Video Creator", shouldLockFeatures)
                }
            >
                <CreativeCard
                    icon={<Video className="h-12 w-12 text-white" />}
                    title="Video Creator"
                    description="Turn your stories into animated videos"
                    bgColor="bg-littlespark-orange"
                    available={false}
                    linkTo="/create/video"
                    isLocked={shouldLockFeatures}
                />
            </div>

            <div
                onClick={() =>
                    handleCardClick("/create/game", true, "Game Designer", shouldLockFeatures)
                }
            >
                <CreativeCard
                    icon={<Gamepad2 className="h-12 w-12 text-white" />}
                    title="Game Designer"
                    description="Design your own game worlds, characters and challenges"
                    bgColor="bg-littlespark-teal"
                    available={false}
                    linkTo="/create/game"
                    isLocked={shouldLockFeatures}
                />
            </div>

            <div
                onClick={() =>
                    handleCardClick(
                        "/create/project",
                        false,
                        "Project Builder",
                        shouldLockFeatures
                    )
                }
            >
                <CreativeCard
                    icon={<FolderOpen className="h-12 w-12 text-white" />}
                    title="Project Builder"
                    description="Organize and create any creative project with AI assistance"
                    bgColor="bg-littlespark-orange"
                    available={false}
                    linkTo="/create/project"
                    isLocked={shouldLockFeatures}
                />
            </div>

            <div
                onClick={() =>
                    handleCardClick(
                        "/create/creature",
                        true,
                        "Code Your Own Creature",
                        shouldLockFeatures
                    )
                }
            >
                <CreativeCard
                    icon={<Code className="h-12 w-12 text-white" />}
                    title="Code Your Own Creature"
                    description="Create your own coded creature and decide how it acts!"
                    bgColor="bg-littlespark-blue"
                    available={false}
                    linkTo="/create/creature"
                    isLocked={shouldLockFeatures}
                />
            </div>
        </div>
    );
};

export default CreativeToolsGrid;
