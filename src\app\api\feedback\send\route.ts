import { NextRequest, NextResponse } from 'next/server';
import { Resend } from 'resend';
import { createServerSupabaseClient } from '@/lib/supabase/server';
import { prisma } from '@/lib/prisma';

const resend = new Resend("re_emkF5t6U_FTwimkZ1SPhwetfdtZPC2FhL");

export async function POST(request: NextRequest) {
  try {
    // Get authenticated user
    const supabase = await createServerSupabaseClient();
    const { data: { user } } = await supabase.auth.getUser();
    
    if (!user) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Authentication required' 
        },
        { status: 401 }
      );
    }

    const { name, email, subject, message, rating } = await request.json();

    // Validate required fields
    if (!name || !email || !message) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Name, email, and message are required' 
        },
        { status: 400 }
      );
    }

    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Valid email is required' 
        },
        { status: 400 }
      );
    }

    // Get user profile for additional context
    const profile = await prisma.profile.findUnique({
      where: { id: user.id },
      select: {
        full_name: true,
        subscription_status: true,
        plan_name: true,
        created_at: true
      }
    });

    // Create email content
    const emailSubject = subject || `New Feedback from ${name}`;
    const emailHtml = `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Little Spark Feedback</title>
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px; }
          .header { background: linear-gradient(135deg, #06b6d4, #0891b2); color: white; padding: 20px; border-radius: 8px; text-align: center; margin-bottom: 20px; }
          .content { background: #f8fafc; padding: 20px; border-radius: 8px; margin-bottom: 20px; }
          .field { margin-bottom: 15px; }
          .label { font-weight: bold; color: #374151; }
          .value { margin-top: 5px; padding: 10px; background: white; border-radius: 4px; border-left: 4px solid #06b6d4; }
          .rating { display: inline-block; background: #fbbf24; color: white; padding: 4px 8px; border-radius: 4px; font-weight: bold; }
          .user-info { background: #e0f2fe; padding: 15px; border-radius: 8px; margin-bottom: 20px; }
          .footer { text-align: center; color: #64748b; font-size: 14px; margin-top: 20px; }
        </style>
      </head>
      <body>
        <div class="header">
          <h1>🎨 New Feedback from Little Spark</h1>
          <p>User Experience Feedback</p>
        </div>
        
        <div class="user-info">
          <h3 style="margin: 0 0 10px 0; color: #0891b2;">User Information</h3>
          <p><strong>Profile Name:</strong> ${profile?.full_name || 'Not provided'}</p>
          <p><strong>User ID:</strong> ${user.id}</p>
          <p><strong>Subscription:</strong> ${profile?.subscription_status || 'Free'} ${profile?.plan_name ? `(${profile.plan_name})` : ''}</p>
          <p><strong>Member Since:</strong> ${profile?.created_at ? new Date(profile.created_at).toLocaleDateString() : 'Unknown'}</p>
        </div>

        <div class="content">
          <div class="field">
            <div class="label">Name:</div>
            <div class="value">${name}</div>
          </div>
          
          <div class="field">
            <div class="label">Email:</div>
            <div class="value">${email}</div>
          </div>
          
          ${subject ? `
          <div class="field">
            <div class="label">Subject:</div>
            <div class="value">${subject}</div>
          </div>
          ` : ''}
          
          ${rating ? `
          <div class="field">
            <div class="label">Rating:</div>
            <div class="value">
              <span class="rating">${rating}/5 Stars</span>
              ${'⭐'.repeat(rating)}
            </div>
          </div>
          ` : ''}
          
          <div class="field">
            <div class="label">Message:</div>
            <div class="value">${message.replace(/\n/g, '<br>')}</div>
          </div>
        </div>
        
        <div class="footer">
          <p>Sent from Little Spark Feedback System</p>
          <p>Received on ${new Date().toLocaleString()}</p>
        </div>
      </body>
      </html>
    `;

    // Send email using Resend
    await resend.emails.send({
      from: '"Little Spark Feedback" <<EMAIL>>',
      to: '<EMAIL>',
      subject: emailSubject,
      html: emailHtml,
      replyTo: email, // Allow direct reply to user
    });

    console.log(`Feedback email <NAME_EMAIL> from ${email}`);

    // Optionally save feedback to database for tracking
    try {
      await prisma.userContent.create({
        data: {
          user_id: user.id,
          type: 'feedback',
          title: subject || 'User Feedback',
          content_metadata: {
            name,
            email,
            subject,
            message,
            rating,
            submittedAt: new Date().toISOString(),
            userAgent: request.headers.get('user-agent') || 'Unknown'
          }
        }
      });
    } catch (logError) {
      console.error('Error saving feedback to database:', logError);
      // Don't fail the request if logging fails
    }

    return NextResponse.json({
      success: true,
      message: 'Feedback sent successfully! Thank you for sharing your experience.'
    });

  } catch (error) {
    console.error('Error sending feedback:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to send feedback. Please try again later.' 
      },
      { status: 500 }
    );
  }
}
