"use client";

import React, { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Save, Download, RefreshCw, Lightbulb, Loader2 } from "lucide-react";
import { toast } from "sonner";
import { useRouter } from "next/navigation";
import { useProject } from "./ProjectContext";
import { saveUserContent } from "@/utils/contentStorage";

const ProjectActionButtons = () => {
    const router = useRouter();
    const {
        title,
        description,
        content,
        projectType,
        isGenerating,
        setIsGenerating,
    } = useProject();

    const [isSaving, setIsSaving] = useState(false);

    const handleSaveProject = async () => {
        if (!title.trim()) {
            toast.error("Please add a title to your project");
            return;
        }

        if (!content.trim()) {
            toast.error("Please add some content to your project");
            return;
        }

        setIsSaving(true);
        try {
            // Check if there's an active challenge
            let challengeId: string | undefined;
            try {
                const currentChallenge = localStorage.getItem('currentChallenge');
                if (currentChallenge) {
                    const challenge = JSON.parse(currentChallenge);
                    // Only link if the challenge type matches the content type
                    if (challenge.type === projectType) {
                        challengeId = challenge.id;
                    }
                }
            } catch (error) {
                console.warn('Failed to parse current challenge from localStorage:', error);
            }

            const savedProject = await saveUserContent({
                type: projectType,
                title: title.trim(),
                content_metadata: {
                    fullContent: content.trim(),
                    description: description.trim(),
                    projectType,
                    preview: content.trim().substring(0, 100) + (content.trim().length > 100 ? "..." : ""),
                } as Record<string, unknown>,
                preview_url: null,
                challenge_id: challengeId
            });

            if (savedProject) {
                toast.success("Project saved successfully!");

                // Trigger event to update dashboard counters
                window.dispatchEvent(new CustomEvent('contentSaved', {
                    detail: { type: projectType }
                }));

                // Redirect to projects section after successful save
                setTimeout(() => {
                    router.push("/my-projects");
                }, 1500);
            } else {
                toast.error("Failed to save project. Please try again.");
            }
        } catch (error) {
            console.error("Error saving project:", error);
            toast.error("Failed to save project. Please try again.");
        } finally {
            setIsSaving(false);
        }
    };

    const handleGetIdeas = async () => {
        if (!title.trim() && !description.trim()) {
            toast.error("Please add a title or description first");
            return;
        }

        setIsGenerating(true);
        try {
            // Simulate AI idea generation
            await new Promise(resolve => setTimeout(resolve, 2000));
            
            const ideas = [
                "Consider adding more details about the main elements",
                "Think about the target audience for your project",
                "What materials or tools will you need?",
                "How can you make this project unique and creative?"
            ];
            
            const randomIdea = ideas[Math.floor(Math.random() * ideas.length)];
            toast.success(`💡 Idea: ${randomIdea}`);
        } catch (error) {
            console.error("Error generating ideas:", error);
            toast.error("Failed to generate ideas. Please try again.");
        } finally {
            setIsGenerating(false);
        }
    };

    const handleDownloadProject = () => {
        if (!title.trim() || !content.trim()) {
            toast.error("Please add title and content before downloading");
            return;
        }

        const projectText = `${title}\n\n${description ? description + '\n\n' : ''}${content}`;
        const blob = new Blob([projectText], { type: 'text/plain' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `${title.replace(/[^a-z0-9]/gi, '_').toLowerCase()}.txt`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
        
        toast.success("Project downloaded!");
    };

    return (
        <div className="flex flex-col items-center gap-4 mb-6">
            <Button
                onClick={handleGetIdeas}
                disabled={isGenerating}
                className="w-full max-w-xl bg-gradient-to-r from-[#4158D0] via-[#C850C0] to-[#FFCC70] hover:opacity-90 text-white font-medium shadow-sm rounded-full transition-all min-h-[44px] text-sm sm:text-base"
            >
                {isGenerating ? (
                    <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                ) : (
                    <Lightbulb className="h-4 w-4 mr-2" />
                )}
                <span className="sm:hidden">Get Ideas</span>
                <span className="hidden sm:inline">Get Creative Ideas</span>
            </Button>

            <div className="flex flex-col sm:flex-row justify-center gap-3 sm:gap-4 w-full max-w-xl">
                <Button
                    variant="outline"
                    onClick={handleSaveProject}
                    disabled={isSaving}
                    className="bg-white border-2 border-[#00BFA5] text-[#00BFA5] hover:bg-[#00BFA5] hover:text-white font-medium px-4 sm:px-8 py-2 rounded-full transition-all min-h-[44px] text-sm sm:text-base w-full sm:w-auto"
                >
                    {isSaving ? (
                        <>
                            <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                            <span className="sm:hidden">Saving...</span>
                            <span className="hidden sm:inline">Saving...</span>
                        </>
                    ) : (
                        <>
                            <Save className="h-4 w-4 mr-2" />
                            <span className="sm:hidden">Save</span>
                            <span className="hidden sm:inline">Save Project</span>
                        </>
                    )}
                </Button>
                
                <Button 
                    variant="outline" 
                    onClick={handleDownloadProject}
                    className="bg-white border-2 border-[#9747FF] text-[#9747FF] hover:bg-[#9747FF] hover:text-white font-medium px-4 sm:px-8 py-2 rounded-full transition-all min-h-[44px] text-sm sm:text-base w-full sm:w-auto"
                >
                    <Download className="h-4 w-4 mr-2" />
                    <span className="sm:hidden">Download</span>
                    <span className="hidden sm:inline">Download</span>
                </Button>
            </div>
        </div>
    );
};

export default ProjectActionButtons;
