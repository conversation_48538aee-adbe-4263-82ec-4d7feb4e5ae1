import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';

// Helper function to verify sync token
function verifySyncToken(req: NextRequest) {
  const authHeader = req.headers.get('authorization');
  const expectedToken = process.env.CMS_SYNC_TOKEN;
  
  if (!authHeader || !authHeader.startsWith('Bearer ') || !expectedToken) {
    return false;
  }
  
  const token = authHeader.substring(7);
  return token === expectedToken;
}

// GET /api/users/sync-status - Get user sync status for CMS
export async function GET(request: NextRequest) {
  try {
    // Verify sync token
    if (!verifySyncToken(request)) {
      return NextResponse.json(
        { error: 'Unauthorized sync request' },
        { status: 401 }
      );
    }

    // Count CMS users in database
    const cmsUsersCount = await prisma.profile.count({
      where: {
        is_cms_user: true
      }
    });

    // Count total users (profiles)
    const totalUsersCount = await prisma.profile.count();

    // Count regular users (non-CMS)
    const regularUsersCount = await prisma.profile.count({
      where: {
        NOT: {
          is_cms_user: true
        }
      }
    });

    return NextResponse.json({
      success: true,
      stats: {
        cmsUsers: cmsUsersCount,
        totalUsers: totalUsersCount,
        regularUsers: regularUsersCount,
        lastSync: 'Not implemented yet' // Could add a sync log table
      }
    });

  } catch (error) {
    console.error('Error checking user sync status:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to check user sync status',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
