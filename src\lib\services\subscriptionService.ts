import { prisma } from '@/lib/prisma';
import { stripe } from '@/lib/stripe';
import Stripe from 'stripe';

export interface SubscriptionData {
  id: string | null;
  status: string | null;
  plan_id: string | null;
  plan_name: string | null;
  billing_cycle: string | null;
  subscription_start: Date | null;
  subscription_end: Date | null;
  trial_end: Date | null;
  trial_used?: boolean;
  stripe_customer_id: string | null;
}

export interface SubscriptionUpdateData {
  status?: string | null;
  plan_id?: string | null;
  plan_name?: string | null;
  billing_cycle?: string | null;
  subscription_start?: Date | null;
  subscription_end?: Date | null;
  trial_end?: Date | null;
  stripe_customer_id?: string | null;
  trial_used?: boolean;
}

export interface PaymentData {
  id: string;
  stripe_payment_id: string;
  amount: number;
  currency: string;
  status: string;
  plan_id: string | null;
  plan_name: string | null;
  payment_date: Date;
  created_at: Date;
}

export class SubscriptionService {
  static async getUserSubscription(userId: string): Promise<{
    subscription: SubscriptionData;
    payments: PaymentData[];
  } | null> {
    try {
      const profile = await prisma.profile.findUnique({
        where: { id: userId },
        include: {
          payments: {
            orderBy: { created_at: 'desc' },
            take: 10
          }
        }
      });

      if (!profile) {
        return null;
      }

      return {
        subscription: {
          id: profile.subscription_id,
          status: profile.subscription_status,
          plan_id: profile.plan_id,
          plan_name: profile.plan_name,
          billing_cycle: profile.billing_cycle,
          subscription_start: profile.subscription_start,
          subscription_end: profile.subscription_end,
          trial_end: profile.trial_end,
          stripe_customer_id: profile.stripe_customer_id
        },
        payments: profile.payments.map(payment => ({
          id: payment.id,
          stripe_payment_id: payment.stripe_payment_id,
          amount: payment.amount.toNumber(),
          currency: payment.currency,
          status: payment.status,
          plan_id: payment.plan_id,
          plan_name: payment.plan_name,
          payment_date: payment.payment_date,
          created_at: payment.created_at
        }))
      };
    } catch (error) {
      console.error('Error fetching user subscription:', error);
      throw error;
    }
  }

  static async updateSubscription(
    userId: string,
    subscriptionData: SubscriptionUpdateData
  ): Promise<void> {
    try {
      // Build the update data object, mapping to the correct Prisma field names
      const updateData: {
        updated_at: Date;
        subscription_status?: string | null;
        plan_id?: string | null;
        plan_name?: string | null;
        billing_cycle?: string | null;
        subscription_start?: Date | null;
        subscription_end?: Date | null;
        trial_end?: Date | null;
        trial_used?: boolean;
        stripe_customer_id?: string | null;
      } = {
        updated_at: new Date()
      };

      if (subscriptionData.status !== undefined) {
        updateData.subscription_status = subscriptionData.status;
      }
      if (subscriptionData.plan_id !== undefined) {
        updateData.plan_id = subscriptionData.plan_id;
      }
      if (subscriptionData.plan_name !== undefined) {
        updateData.plan_name = subscriptionData.plan_name;
      }
      if (subscriptionData.billing_cycle !== undefined) {
        updateData.billing_cycle = subscriptionData.billing_cycle;
      }
      if (subscriptionData.subscription_start !== undefined) {
        updateData.subscription_start = subscriptionData.subscription_start;
      }
      if (subscriptionData.subscription_end !== undefined) {
        updateData.subscription_end = subscriptionData.subscription_end;
      }
      if (subscriptionData.trial_end !== undefined) {
        updateData.trial_end = subscriptionData.trial_end;
      }
      if (subscriptionData.stripe_customer_id !== undefined) {
        updateData.stripe_customer_id = subscriptionData.stripe_customer_id;
      }
      if (subscriptionData.trial_used !== undefined) {
        updateData.trial_used = subscriptionData.trial_used;
      }

      await prisma.profile.update({
        where: { id: userId },
        data: updateData
      });
    } catch (error) {
      console.error('Error updating subscription:', error);
      throw error;
    }
  }

  static async createPaymentRecord(
    userId: string,
    paymentData: Omit<PaymentData, 'id' | 'created_at'>
  ): Promise<void> {
    try {
      await prisma.payment.create({
        data: {
          profile_id: userId,
          ...paymentData,
          created_at: new Date()
        }
      });
    } catch (error) {
      console.error('Error creating payment record:', error);
      throw error;
    }
  }

  static async cancelSubscription(
    userId: string,
    immediate: boolean = false
  ): Promise<boolean> {
    try {
      const profile = await prisma.profile.findUnique({
        where: { id: userId }
      });

      if (!profile?.subscription_id || !stripe) {
        return false;
      }

      // Fetch current subscription to check trial status
      const currentSubscription = await stripe.subscriptions.retrieve(profile.subscription_id);
      const isTrialing = currentSubscription.status === 'trialing';

      // Cancel in Stripe
      let canceledSubscription: Stripe.Subscription & { current_period_end?: number };
      if (isTrialing || immediate) {
        // For trials or immediate cancellation, cancel right away
        canceledSubscription = await stripe.subscriptions.cancel(profile.subscription_id) as Stripe.Subscription & { current_period_end?: number };
      } else {
        // For active subscriptions, cancel at period end
        canceledSubscription = await stripe.subscriptions.update(profile.subscription_id, {
          cancel_at_period_end: true
        }) as Stripe.Subscription & { current_period_end?: number };
      }

      // Determine new status and end date
      const newStatus = isTrialing || immediate ? 'canceled' : 'cancel_at_period_end';
      const subscriptionEnd = canceledSubscription.current_period_end 
        ? new Date(canceledSubscription.current_period_end * 1000) 
        : new Date();

      // Update in database - mark trial as used if cancelling trial
      await this.updateSubscription(userId, {
        status: newStatus,
        subscription_end: subscriptionEnd,
        trial_end: isTrialing ? new Date() : undefined,
        trial_used: isTrialing ? true : undefined // Mark trial as used when cancelling trial
      });

      return true;
    } catch (error) {
      console.error('Error canceling subscription:', error);
      throw error;
    }
  }

  static async isSubscriptionActive(userId: string): Promise<boolean> {
    try {
      const subscription = await this.getUserSubscription(userId);

      if (!subscription?.subscription.status) {
        return false;
      }

      // Include 'incomplete' as active since payment may have succeeded
      const activeStatuses = ['active', 'trialing', 'incomplete'];
      return activeStatuses.includes(subscription.subscription.status);
    } catch (error) {
      console.error('Error checking subscription status:', error);
      return false;
    }
  }

  static async handleImmediateUpgrade(subscriptionId: string, paymentMethodId: string) {
    try {
      if (!stripe) {
        throw new Error('Stripe not configured');
      }

      // Get the subscription and its latest invoice
      const subscription = await stripe.subscriptions.retrieve(subscriptionId, {
        expand: ['latest_invoice']
      });

      if (!subscription.latest_invoice || typeof subscription.latest_invoice === 'string') {
        throw new Error('Invalid invoice data');
      }

      // If there's a prorated amount, pay it immediately
      if (subscription.latest_invoice.amount_due > 0) {
        await stripe.paymentIntents.create({
          amount: subscription.latest_invoice.amount_due,
          currency: subscription.latest_invoice.currency,
          payment_method: paymentMethodId,
          customer: subscription.customer as string,
          confirm: true,
          off_session: true,
          description: 'Immediate plan upgrade payment'
        });
      }

      return true;
    } catch (error) {
      console.error('Error handling immediate upgrade payment:', error);
      throw error;
    }
  }
} 