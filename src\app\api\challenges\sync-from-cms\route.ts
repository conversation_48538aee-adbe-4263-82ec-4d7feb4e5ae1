import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';

// Helper function to verify sync token
function verifySyncToken(req: NextRequest) {
  const authHeader = req.headers.get('authorization');
  const expectedToken = process.env.CMS_SYNC_TOKEN;
  
  if (!authHeader || !authHeader.startsWith('Bearer ') || !expectedToken) {
    return false;
  }
  
  const token = authHeader.substring(7);
  return token === expectedToken;
}

// POST /api/challenges/sync-from-cms - Receive synced challenge from CMS
export async function POST(request: NextRequest) {
  try {
    // Verify sync token
    if (!verifySyncToken(request)) {
      return NextResponse.json(
        { error: 'Unauthorized sync request' },
        { status: 401 }
      );
    }

    const challengeData = await request.json();

    console.log(`🔄 [MAIN-APP-SYNC] Receiving challenge from CMS:`, {
      cms_id: challengeData.cms_id,
      title: challengeData.title,
      category: challengeData.category,
      difficulty: challengeData.difficulty
    });

    // Validate required fields
    if (!challengeData.cms_id || !challengeData.title) {
      return NextResponse.json(
        { error: 'Missing required fields: cms_id, title' },
        { status: 400 }
      );
    }

    // Check if challenge already exists by cms_id
    const existingChallenge = await prisma.challenge.findFirst({
      where: { cms_id: challengeData.cms_id }
    });

    if (existingChallenge) {
      // Update existing challenge
      const updatedChallenge = await prisma.challenge.update({
        where: { id: existingChallenge.id },
        data: {
          title: challengeData.title,
          description: challengeData.description || challengeData.title,
          difficulty: challengeData.difficulty || 'beginner',
          type: challengeData.category || 'story',
          prompt: challengeData.prompt || challengeData.description || challengeData.title,
          is_active: true,
          updated_at: new Date(),
        }
      });

      console.log(`✅ [MAIN-APP-SYNC] Updated challenge: ${challengeData.title}`);

      return NextResponse.json({
        success: true,
        action: 'updated',
        challenge: updatedChallenge
      });
    } else {
      // Create new challenge
      const newChallenge = await prisma.challenge.create({
        data: {
          id: `cms-${challengeData.cms_id}`, // Generate unique ID
          cms_id: challengeData.cms_id,
          title: challengeData.title,
          description: challengeData.description || challengeData.title, // Fallback to title if no description
          difficulty: challengeData.difficulty || 'beginner',
          type: challengeData.category || 'story',
          prompt: challengeData.prompt || challengeData.description || challengeData.title,
          is_active: true,
          created_by: 'cms',
        }
      });
      
      console.log(`🆕 [MAIN-APP-SYNC] Created challenge: ${challengeData.title}`);
      
      return NextResponse.json({
        success: true,
        action: 'created',
        challenge: newChallenge
      });
    }

  } catch (error) {
    console.error('❌ [MAIN-APP-SYNC] Error syncing challenge from CMS:', error);
    return NextResponse.json(
      { 
        success: false,
        error: 'Failed to sync challenge from CMS',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
