import { supabase } from '@/lib/supabase/client';
import { AuthError, Session, User } from '@supabase/supabase-js';
import { AuthApiService } from '@/lib/api';

export interface SignUpData {
  email: string;
  password: string;
  fullName: string;
}

export interface SignInData {
  email: string;
  password: string;
}

export interface MagicLinkSignUpData {
  email: string;
  fullName: string;
}

export interface AuthResult {
  user: User | null;
  session: Session | null;
  error: AuthError | null;
}

class AuthService {
  // Sign up with email and password
  async signUp({ email, password, fullName }: SignUpData): Promise<AuthResult> {
    try {
      const { data, error } = await supabase.auth.signUp({
        email,
        password,
        options: {
          data: {
            full_name: fullName,
          }
        }
      });

      if (error) {
        return { user: null, session: null, error };
      }

      // Create user profile
      if (data.user) {
        await this.createUserProfile(data.user.id, email, fullName);
      }

      return { user: data.user, session: data.session, error: null };
    } catch (error) {
      return { 
        user: null, 
        session: null, 
        error: error as AuthError 
      };
    }
  }

  // Sign in with email and password
  async signIn({ email, password }: SignInData): Promise<AuthResult> {
    try {
      const { data, error } = await supabase.auth.signInWithPassword({
        email,
        password,
      });

      return { user: data.user, session: data.session, error };
    } catch (error) {
      return { 
        user: null, 
        session: null, 
        error: error as AuthError 
      };
    }
  }

  // Sign out (client-side first, then backend)
  async signOut(): Promise<{ error: AuthError | null }> {
    try {
      // First, clear client-side session
      const { error: clientError } = await supabase.auth.signOut();
      
      // Then call backend to clear server-side cookies (ignore errors)
      try {
        await AuthApiService.signOut();
      } catch (backendError) {
        // Ignore backend errors for signout - client-side is more important
        console.warn('Backend signout failed, but client signout succeeded:', backendError);
      }

      return { error: clientError };
    } catch (error) {
      console.error('Sign out error:', error);
      return { error: { message: 'Network error' } as AuthError };
    }
  }

  // Get current session
  async getSession(): Promise<{ session: Session | null; error: AuthError | null }> {
    const { data, error } = await supabase.auth.getSession();
    return { session: data.session, error };
  }

  // Get current user
  async getUser(): Promise<{ user: User | null; error: AuthError | null }> {
    const { data, error } = await supabase.auth.getUser();
    return { user: data.user, error };
  }

  // Reset password
  async resetPassword(email: string): Promise<{ error: AuthError | null }> {
    const { error } = await supabase.auth.resetPasswordForEmail(email);
    return { error };
  }

  // Update password
  async updatePassword(password: string): Promise<{ error: AuthError | null }> {
    const { error } = await supabase.auth.updateUser({ password });
    return { error };
  }

  // Create user profile in database
  private async createUserProfile(userId: string, email: string, fullName: string) {
    const { error } = await supabase
      .from('profiles')
      .insert({
        id: userId,
        email,
        full_name: fullName,
      });

    if (error) {
      console.error('Error creating user profile:', error);
    }
  }

  // Sign in with magic link (using backend API)
  async signInWithMagicLink(email: string): Promise<{ error: AuthError | null; userNotFound?: boolean }> {
    try {
      await AuthApiService.signIn({ email });
      return { error: null };
    } catch (error) {
      console.error('Magic link sign in error:', error);
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';

      // Check if this is a user not found error
      const isUserNotFound = errorMessage.includes('Account not found') ||
                            errorMessage.includes('User not found') ||
                            errorMessage.includes('Invalid login credentials');

      return {
        error: { message: errorMessage } as AuthError,
        userNotFound: isUserNotFound
      };
    }
  }

  // Sign up with magic link (using backend API)
  async signUpWithMagicLink({ email, fullName }: MagicLinkSignUpData): Promise<{ error: AuthError | null }> {
    try {
      await AuthApiService.signUp({ email, fullName });
      return { error: null };
    } catch (error) {
      console.error('Magic link sign up error:', error);
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      return { error: { message: errorMessage } as AuthError };
    }
  }

  // Listen to auth state changes
  onAuthStateChange(callback: (event: string, session: Session | null) => void) {
    return supabase.auth.onAuthStateChange(callback);
  }
}

export const authService = new AuthService(); 