
import React, { useState } from "react";
import { Question } from "./types";
import { Button } from '@/components/ui/button';

interface ActiveQuestionProps {
    currentQuestion: Question;
    onSubmitAnswer: (answer: string) => void; 
    onNextQuestion: () => void;
    isSubmitting?: boolean;
}

const ActiveQuestion: React.FC<ActiveQuestionProps> = ({
    currentQuestion,
    onSubmitAnswer,
    onNextQuestion,
    isSubmitting = false,
}) => {
    const [answer, setAnswer] = useState("");
    const [selectedOption, setSelectedOption] = useState("");
    const [hasSubmitted, setHasSubmitted] = useState(false);

    const handleSubmit = () => {
        const finalAnswer = currentQuestion.question_type === 'mcq' ? selectedOption : answer.trim();
        if (finalAnswer.length === 0) return;

        onSubmitAnswer(finalAnswer);
        setHasSubmitted(true);
    };

    const handleNext = () => {
        setAnswer("");
        setSelectedOption("");
        setHasSubmitted(false);
        onNextQuestion();
    };

    // Check if question is already answered
    const isAlreadyAnswered = currentQuestion.is_answered || currentQuestion.user_answer;

    return (
        <div className="space-y-6">
            <div className="bg-gradient-to-r from-spark-blue/10 to-spark-purple/10 p-6 rounded-lg">
                <div className="flex items-start gap-3 mb-4">
                    <div className="h-6 w-6 bg-spark-blue text-white rounded-full flex items-center justify-center text-sm font-bold flex-shrink-0 mt-1">
                        ?
                    </div>
                    <div className="flex-1">
                        <h3 className="text-lg font-semibold text-gray-900 mb-2">
                            {currentQuestion.question}
                        </h3>
                        <div className="flex gap-2 text-xs">
                            <span className="px-2 py-1 bg-spark-blue/20 text-spark-blue rounded-full">
                                {currentQuestion.difficulty}
                            </span>
                            <span className="px-2 py-1 bg-spark-purple/20 text-spark-purple rounded-full">
                                {currentQuestion.category}
                            </span>
                            {currentQuestion.is_ai_generated && (
                                <span className="px-2 py-1 bg-green-100 text-green-700 rounded-full">
                                    🤖 AI Generated
                                </span>
                            )}
                        </div>
                    </div>
                </div>

                {/* Show previous answer if already answered */}
                {isAlreadyAnswered && currentQuestion.user_answer && (
                    <div className={`mt-4 p-4 border rounded-lg ${
                        currentQuestion.user_answer.is_correct === true
                            ? 'bg-green-50 border-green-200'
                            : currentQuestion.user_answer.is_correct === false
                            ? 'bg-red-50 border-red-200'
                            : 'bg-blue-50 border-blue-200'
                    }`}>
                        <div className="flex items-center gap-2 mb-2">
                            <h4 className={`font-semibold ${
                                currentQuestion.user_answer.is_correct === true
                                    ? 'text-green-900'
                                    : currentQuestion.user_answer.is_correct === false
                                    ? 'text-red-900'
                                    : 'text-blue-900'
                            }`}>
                                Your Previous Answer:
                            </h4>
                            {currentQuestion.user_answer.is_correct === true && (
                                <span className="text-green-600 text-sm">✓ Correct</span>
                            )}
                            {currentQuestion.user_answer.is_correct === false && (
                                <span className="text-red-600 text-sm">✗ Incorrect</span>
                            )}
                        </div>
                        <p className={`${
                            currentQuestion.user_answer.is_correct === true
                                ? 'text-green-800'
                                : currentQuestion.user_answer.is_correct === false
                                ? 'text-red-800'
                                : 'text-blue-800'
                        }`}>
                            {currentQuestion.user_answer.answer}
                        </p>
                        {currentQuestion.question_type === 'mcq' && currentQuestion.user_answer.is_correct === false && (
                            <p className="text-green-700 mt-2 text-sm">
                                <strong>Correct answer:</strong> {currentQuestion.correct_answer}
                            </p>
                        )}
                        <p className={`text-xs mt-2 ${
                            currentQuestion.user_answer.is_correct === true
                                ? 'text-green-600'
                                : currentQuestion.user_answer.is_correct === false
                                ? 'text-red-600'
                                : 'text-blue-600'
                        }`}>
                            Answered on {new Date(currentQuestion.user_answer.answered_at).toLocaleDateString()}
                        </p>
                    </div>
                )}

                {/* Answer input for new questions */}
                {!isAlreadyAnswered && !hasSubmitted && (
                    <div className="mt-4">
                        {currentQuestion.question_type === 'mcq' && currentQuestion.options ? (
                            <div>
                                <label className="block text-sm font-medium text-gray-700 mb-3">
                                    Choose your answer:
                                </label>
                                <div className="space-y-2">
                                    {currentQuestion.options.map((option, index) => (
                                        <label
                                            key={index}
                                            className={`flex items-center p-3 border rounded-lg cursor-pointer transition-colors ${
                                                selectedOption === option
                                                    ? 'border-spark-blue bg-spark-blue/10'
                                                    : 'border-gray-300 hover:border-spark-blue/50'
                                            }`}
                                        >
                                            <input
                                                type="radio"
                                                name="mcq-option"
                                                value={option}
                                                checked={selectedOption === option}
                                                onChange={(e) => setSelectedOption(e.target.value)}
                                                className="mr-3 text-spark-blue focus:ring-spark-blue"
                                                disabled={isSubmitting}
                                            />
                                            <span className="text-gray-900">{option}</span>
                                        </label>
                                    ))}
                                </div>
                            </div>
                        ) : (
                            <div>
                                <label htmlFor="answer" className="block text-sm font-medium text-gray-700 mb-2">
                                    Your Answer:
                                </label>
                                <textarea
                                    id="answer"
                                    value={answer}
                                    onChange={(e) => setAnswer(e.target.value)}
                                    placeholder="Share your thoughts here..."
                                    className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-spark-blue focus:border-transparent resize-none"
                                    rows={4}
                                    disabled={isSubmitting}
                                />
                                <p className="text-xs text-gray-500 mt-1">
                                    Take your time to think and express your ideas clearly.
                                </p>
                            </div>
                        )}
                    </div>
                )}

                {/* Show submitted answer */}
                {hasSubmitted && !isAlreadyAnswered && (
                    <div className="mt-4 p-4 bg-blue-50 border border-blue-200 rounded-lg">
                        <h4 className="font-semibold text-blue-900 mb-2">
                            Your Answer:
                        </h4>
                        <p className="text-blue-800">
                            {currentQuestion.question_type === 'mcq' ? selectedOption : answer}
                        </p>
                    </div>
                )}
            </div>

            <div className="flex gap-3">
                {!isAlreadyAnswered && !hasSubmitted ? (
                    <Button
                        onClick={handleSubmit}
                        disabled={
                            (currentQuestion.question_type === 'mcq' ? selectedOption.length === 0 : answer.trim().length === 0) ||
                            isSubmitting
                        }
                        className="flex-1"
                    >
                        {isSubmitting ? 'Saving...' : 'Submit Answer'}
                    </Button>
                ) : (
                    <Button
                        onClick={handleNext}
                        className="flex-1"
                    >
                        Next Question
                    </Button>
                )}
            </div>
        </div>
    );
};

export default ActiveQuestion;
