import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { createServerSupabaseClient } from '@/lib/supabase/server';

// POST /api/challenges/mark-complete - Mark challenge as completed after validation
export async function POST(request: NextRequest) {
  try {
    // Get authenticated user
    const supabase = await createServerSupabaseClient();
    const { data: { user } } = await supabase.auth.getUser();
    
    if (!user) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Authentication required' 
        },
        { status: 401 }
      );
    }

    const { challengeId, contentId, challengeStartedAt } = await request.json();

    if (!challengeId) {
      return NextResponse.json(
        {
          success: false,
          error: 'Challenge ID is required'
        },
        { status: 400 }
      );
    }

    // Convert challengeId to string if it's a number
    const challengeIdString = String(challengeId);

    // Get challenge details (handle both database and CMS challenges)
    const isCMSChallenge = /^[a-z]+$/.test(challengeIdString) && challengeIdString.length <= 3;
    let challenge;

    if (isCMSChallenge) {
      // First try to find the synced CMS challenge in database
      const syncedChallenge = await prisma.challenge.findUnique({
        where: { id: `cms-${challengeIdString}` }
      });

      if (syncedChallenge) {
        challenge = syncedChallenge;
      } else {
        // Fallback: create a mock challenge object for legacy CMS challenges
        challenge = {
          id: challengeIdString,
          type: 'unknown', // Will be determined from content
          title: `CMS Challenge ${challengeIdString.toUpperCase()}`,
          description: 'CMS-created challenge'
        };
      }
    } else {
      challenge = await prisma.challenge.findUnique({
        where: { id: challengeIdString }
      });

      if (!challenge) {
        return NextResponse.json(
          {
            success: false,
            error: 'Challenge not found'
          },
          { status: 404 }
        );
      }
    }

    // Check if user already completed this challenge
    const existingCompletion = await prisma.challengeCompletion.findUnique({
      where: {
        user_id_challenge_id: {
          user_id: user.id,
          challenge_id: challengeIdString
        }
      }
    });

    if (existingCompletion) {
      return NextResponse.json({
        success: true,
        alreadyCompleted: true,
        message: 'Challenge already completed!',
        completion: existingCompletion
      });
    }

    // Enhanced validation: Find the content to link to this completion
    let targetContent;

    if (contentId) {
      // Use specific content if provided
      targetContent = await prisma.userContent.findFirst({
        where: {
          id: contentId,
          user_id: user.id,
          // For CMS challenges, don't validate type since we don't know it yet
          ...(isCMSChallenge ? {} : { type: challenge.type }),
          // If challengeStartedAt is provided, validate timing
          ...(challengeStartedAt && {
            created_at: {
              gte: new Date(challengeStartedAt)
            }
          })
        }
      });

      // For CMS challenges, update the challenge type based on content
      if (isCMSChallenge && targetContent) {
        challenge.type = targetContent.type;
      }
    } else {
      // For CMS challenges, find the most recent content of any type
      if (isCMSChallenge) {
        const timeLimit = challengeStartedAt
          ? new Date(challengeStartedAt)
          : new Date(Date.now() - 7 * 24 * 60 * 60 * 1000);

        targetContent = await prisma.userContent.findFirst({
          where: {
            user_id: user.id,
            created_at: {
              gte: timeLimit
            }
          },
          orderBy: {
            created_at: 'desc'
          }
        });

        // Update challenge type based on content found
        if (targetContent) {
          challenge.type = targetContent.type;
        }
      } else {
        // Find the most recent matching content with enhanced validation
        const timeLimit = challengeStartedAt
          ? new Date(challengeStartedAt)
          : new Date(Date.now() - 7 * 24 * 60 * 60 * 1000);

        targetContent = await prisma.userContent.findFirst({
          where: {
            user_id: user.id,
            type: challenge.type,
            created_at: {
              gte: timeLimit
            }
          },
          orderBy: {
            created_at: 'desc'
          }
        });
      }
    }

    // Additional validation: Ensure content meets challenge requirements
    if (targetContent && challengeStartedAt) {
      const challengeStartTime = new Date(challengeStartedAt);
      const contentCreatedTime = new Date(targetContent.created_at);

      // Verify content was created after challenge was started
      if (contentCreatedTime < challengeStartTime) {
        return NextResponse.json({
          success: false,
          error: `Content was created before starting the challenge. Please create new ${challenge.type} content after starting the challenge.`,
          requiredType: challenge.type
        }, { status: 400 });
      }
    }

    if (!targetContent) {
      return NextResponse.json({
        success: false,
        error: `No ${challenge.type} content found. Please create content first!`,
        requiredType: challenge.type
      }, { status: 400 });
    }

    // Create the challenge completion record
    // For CMS challenges, we need to handle the completion differently since they're not in the database
    let completion;

    if (isCMSChallenge) {
      // For CMS challenges, create a completion record without foreign key constraint
      completion = await prisma.challengeCompletion.create({
        data: {
          user_id: user.id,
          challenge_id: challengeIdString,
          content_id: targetContent.id
        },
        include: {
          content: {
            select: {
              id: true,
              title: true,
              type: true,
              preview_url: true
            }
          }
        }
      });

      // Add challenge info manually for CMS challenges - skip for now
    } else {
      completion = await prisma.challengeCompletion.create({
        data: {
          user_id: user.id,
          challenge_id: challengeIdString,
          content_id: targetContent.id
        },
        include: {
          challenge: {
            select: {
              id: true,
              title: true,
              type: true,
              difficulty: true
            }
          },
          content: {
            select: {
              id: true,
              title: true,
              type: true,
              preview_url: true
            }
          }
        }
      });
    }

    // Optionally update the content to link it to the challenge
    if (!targetContent.challenge_id) {
      await prisma.userContent.update({
        where: { id: targetContent.id },
        data: { challenge_id: challengeIdString }
      });
    }

    return NextResponse.json({
      success: true,
      message: 'Challenge completed successfully!',
      completion: {
        id: completion.id,
        challengeId: completion.challenge_id,
        challengeTitle: challenge.title,
        challengeType: challenge.type,
        difficulty: 'medium', // Default for CMS challenges
        contentId: completion.content_id,
        contentTitle: completion.content.title,
        contentPreviewUrl: completion.content.preview_url,
        completedAt: completion.completed_at
      }
    });

  } catch (error) {
    console.error('Error marking challenge as complete:', error);
    
    // Handle unique constraint violation (user already completed this challenge)
    if (error instanceof Error && error.message.includes('Unique constraint')) {
      return NextResponse.json({
        success: false,
        error: 'Challenge already completed',
        alreadyCompleted: true
      }, { status: 409 });
    }

    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to mark challenge as complete' 
      },
      { status: 500 }
    );
  }
}
