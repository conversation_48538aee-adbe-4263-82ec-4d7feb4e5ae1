import { NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';

/**
 * Quick fix API to immediately sync subscription statuses for accounts with mismatches
 * This is a targeted fix for the specific issue where accounts have payments but still show 'trialing'
 */
export async function POST() {
  try {
    console.log('🔧 Starting subscription status sync...');
    
    // Find all accounts with trialing status that have successful payments
    const trialingAccountsWithPayments = await prisma.profile.findMany({
      where: {
        subscription_status: 'trialing'
      },
      include: {
        payments: {
          where: {
            status: 'succeeded'
          },
          orderBy: {
            created_at: 'desc'
          },
          take: 1
        }
      }
    });

    console.log(`Found ${trialingAccountsWithPayments.length} accounts with trialing status`);

    const fixedAccounts = [];
    
    for (const account of trialingAccountsWithPayments) {
      if (account.payments.length > 0) {
        const latestPayment = account.payments[0];
        
        console.log(`🔄 Fixing account ${account.email}: trialing -> active (payment: $${latestPayment.amount})`);
        
        // Update status to active
        await prisma.profile.update({
          where: { id: account.id },
          data: {
            subscription_status: 'active',
            trial_used: true,
            updated_at: new Date()
          }
        });

        fixedAccounts.push({
          email: account.email,
          previousStatus: 'trialing',
          newStatus: 'active',
          latestPaymentAmount: latestPayment.amount,
          latestPaymentDate: latestPayment.payment_date,
          paymentId: latestPayment.stripe_payment_id
        });
      }
    }

    // Also fix incomplete accounts with payments
    const incompleteAccountsWithPayments = await prisma.profile.findMany({
      where: {
        subscription_status: 'incomplete'
      },
      include: {
        payments: {
          where: {
            status: 'succeeded'
          },
          orderBy: {
            created_at: 'desc'
          },
          take: 1
        }
      }
    });

    console.log(`Found ${incompleteAccountsWithPayments.length} accounts with incomplete status`);

    for (const account of incompleteAccountsWithPayments) {
      if (account.payments.length > 0) {
        const latestPayment = account.payments[0];
        
        console.log(`🔄 Fixing account ${account.email}: incomplete -> active (payment: $${latestPayment.amount})`);
        
        // Update status to active
        await prisma.profile.update({
          where: { id: account.id },
          data: {
            subscription_status: 'active',
            updated_at: new Date()
          }
        });

        fixedAccounts.push({
          email: account.email,
          previousStatus: 'incomplete',
          newStatus: 'active',
          latestPaymentAmount: latestPayment.amount,
          latestPaymentDate: latestPayment.payment_date,
          paymentId: latestPayment.stripe_payment_id
        });
      }
    }

    console.log(`✅ Fixed ${fixedAccounts.length} accounts`);

    return NextResponse.json({
      success: true,
      message: `Successfully fixed ${fixedAccounts.length} subscription status mismatches`,
      fixedAccounts,
      summary: {
        totalAccountsChecked: trialingAccountsWithPayments.length + incompleteAccountsWithPayments.length,
        accountsFixed: fixedAccounts.length,
        trialingFixed: fixedAccounts.filter(a => a.previousStatus === 'trialing').length,
        incompleteFixed: fixedAccounts.filter(a => a.previousStatus === 'incomplete').length
      }
    });

  } catch (error) {
    console.error('❌ Error fixing subscription sync:', error);
    return NextResponse.json(
      { 
        success: false,
        error: 'Failed to fix subscription statuses', 
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

/**
 * GET endpoint to check which accounts need fixing without making changes
 */
export async function GET() {
  try {
    // Check trialing accounts with payments
    const trialingWithPayments = await prisma.profile.findMany({
      where: {
        subscription_status: 'trialing'
      },
      include: {
        payments: {
          where: {
            status: 'succeeded'
          }
        }
      }
    });

    // Check incomplete accounts with payments
    const incompleteWithPayments = await prisma.profile.findMany({
      where: {
        subscription_status: 'incomplete'
      },
      include: {
        payments: {
          where: {
            status: 'succeeded'
          }
        }
      }
    });

    const trialingNeedingFix = trialingWithPayments.filter(a => a.payments.length > 0);
    const incompleteNeedingFix = incompleteWithPayments.filter(a => a.payments.length > 0);

    return NextResponse.json({
      needsFixing: trialingNeedingFix.length + incompleteNeedingFix.length,
      trialingWithPayments: trialingNeedingFix.length,
      incompleteWithPayments: incompleteNeedingFix.length,
      accounts: [
        ...trialingNeedingFix.map(a => ({
          email: a.email,
          currentStatus: 'trialing',
          paymentCount: a.payments.length,
          latestPayment: a.payments[0]?.payment_date
        })),
        ...incompleteNeedingFix.map(a => ({
          email: a.email,
          currentStatus: 'incomplete',
          paymentCount: a.payments.length,
          latestPayment: a.payments[0]?.payment_date
        }))
      ]
    });

  } catch (error) {
    console.error('Error checking accounts needing fix:', error);
    return NextResponse.json(
      { error: 'Failed to check accounts', details: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    );
  }
}
