/* eslint-disable @typescript-eslint/no-explicit-any, prefer-const */
import { useState } from 'react';
import { toast } from 'sonner';
import { useRouter } from 'next/navigation';
import { generateMusic } from '@/utils/ai/musicService';
import { saveUserContent } from '@/utils/contentStorage';

interface UseMusicGeneratorProps {
  onGenerate: () => void;
  onGenerationError?: (error: string) => void;
}

export const useMusicGenerator = ({
  onGenerate,
  onGenerationError
}: UseMusicGeneratorProps) => {
  const router = useRouter();
  const [musicTitle, setMusicTitle] = useState<string>('');
  const [musicDescription, setMusicDescription] = useState<string>('');
  const [songTheme, setSongTheme] = useState<string>('');
  const [musicLyrics, setMusicLyrics] = useState<string>('');
  const [musicDuration, setMusicDuration] = useState<number>(10); // Best quality setting
  const [selectedStyle, setSelectedStyle] = useState('orchestral');
  const [selectedMood, setSelectedMood] = useState('');
  const [isGeneratingDescription, setIsGeneratingDescription] = useState(false);
  const [isImprovingDescription, setIsImprovingDescription] = useState(false);
  const [isGeneratingMusic, setIsGeneratingMusic] = useState(false);
  const [isGeneratingLyrics, setIsGeneratingLyrics] = useState(false);
  const [generatedAudioUrl, setGeneratedAudioUrl] = useState<string | null>(null);
  const [generationId, setGenerationId] = useState<string | null>(null);
  const [isSaving, setIsSaving] = useState(false);

  const handleGenerateDescription = async () => {
    setIsGeneratingDescription(true);
    // Clear previous lyrics when generating a new idea
    setMusicLyrics('');
    try {
      const response = await fetch('/api/music/idea', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ style: selectedStyle, mood: selectedMood, theme: songTheme, duration: musicDuration })
      });
      const data = await response.json();
      
      // Check if the API already parsed the JSON for us
      if (data.title && data.description) {
        setMusicTitle(data.title);
        setMusicDescription(data.description);
      } else {
        // Fallback: try to parse the raw idea text
        let ideaText: string = data.idea;
        let cleaned = ideaText.trim();
        // Strip markdown code fences and any extra text
        cleaned = cleaned.replace(/^```(?:json)?\s*/, '').replace(/```$/, '').trim();
        // Extract JSON if it's wrapped in other text
        const jsonMatch = cleaned.match(/\{[\s\S]*\}/);
        if (jsonMatch) {
          cleaned = jsonMatch[0];
        }
        let parsed: any = null;
        try { parsed = JSON.parse(cleaned); } catch { /* not JSON */ }
        if (parsed && parsed.title && parsed.description) {
          setMusicTitle(parsed.title);
          setMusicDescription(parsed.description);
        } else {
          setMusicDescription(cleaned);
          const titleWords = cleaned.split(' ').slice(0,3).join(' ');
          setMusicTitle(titleWords);
        }
      }
      toast.success('Generated music description!');
    } catch (error) {
      console.error("Error generating music description:", error);
      toast.error("Failed to generate music description. Please try again.");
    } finally {
      setIsGeneratingDescription(false);
    }
  };
 
  const handleImproveDescription = async () => {
    if (!musicDescription) {
      toast.error('No existing idea to improve. Please generate an idea first.');
      return;
    }
    setIsImprovingDescription(true);
    // Clear previous lyrics when improving idea
    setMusicLyrics('');
    try {
      const response = await fetch('/api/music/idea/improve', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ existingDescription: musicDescription, style: selectedStyle, mood: selectedMood, theme: songTheme, duration: musicDuration })
      });
      const data = await response.json();
      
      // Check if the API already parsed the JSON for us
      if (data.title && data.description) {
        setMusicTitle(data.title);
        setMusicDescription(data.description);
      } else {
        // Fallback: try to parse the raw idea text
        let ideaText: string = data.idea;
        let cleaned = ideaText.trim();
        // Strip markdown code fences and any extra text
        cleaned = cleaned.replace(/^```(?:json)?\s*/, '').replace(/```$/, '').trim();
        // Extract JSON if it's wrapped in other text
        const jsonMatch = cleaned.match(/\{[\s\S]*\}/);
        if (jsonMatch) {
          cleaned = jsonMatch[0];
        }
        let parsed: any = null;
        try { parsed = JSON.parse(cleaned); } catch { /* ignore */ }
        if (parsed && parsed.title && parsed.description) {
          setMusicTitle(parsed.title);
          setMusicDescription(parsed.description);
        }
      }
      toast.success('Improved music description!');
    } catch (error) {
      console.error('Error improving music description:', error);
      toast.error('Failed to improve music description. Please try again.');
      if (onGenerationError) onGenerationError(error instanceof Error ? error.message : String(error));
    } finally {
      setIsImprovingDescription(false);
    }
  };
  
  const handleGenerate = async () => {
    if (musicDescription.length < 10) {
      toast.error('Please provide a more detailed description for better results');
      return;
    }
    
    setIsGeneratingMusic(true);

    // Show informative toast about generation time
    toast.info('🎵 Music generation started! This typically takes 1 minutes. Please keep this tab open.', {
      duration: 8000,
    });

    try {
      const result = await generateMusic({
        prompt: musicDescription,
        style: selectedStyle,
        mood: selectedMood,
        duration: musicDuration,
        title: musicTitle
      });
      
      setGeneratedAudioUrl(result.audioUrl);
      setGenerationId(result.generationId);
      
      toast.success('Music generated successfully!');
      onGenerate();
    } catch (error) {
      console.error("Error generating music:", error);
      const errorMessage = error instanceof Error 
        ? error.message 
        : "Failed to generate music. Please try again.";
      
      if (onGenerationError) {
        onGenerationError(errorMessage);
      }
      
      toast.error(errorMessage);
    } finally {
      setIsGeneratingMusic(false);
    }
  };

  // Handler to generate lyrics separately
  const handleGenerateLyrics = async () => {
    if (!musicTitle || !musicDescription) {
      toast.error('Please generate an idea before generating lyrics');
      return;
    }
    setIsGeneratingLyrics(true);
    try {
      const response = await fetch('/api/music/lyrics', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ title: musicTitle, description: musicDescription, style: selectedStyle, mood: selectedMood, theme: songTheme, duration: musicDuration })
      });
      const data = await response.json();
      setMusicLyrics(data.lyrics);
      toast.success('Generated lyrics!');
    } catch (error) {
      console.error('Error generating lyrics:', error);
      toast.error('Failed to generate lyrics. Please try again.');
    } finally {
      setIsGeneratingLyrics(false);
    }
  };

  const handleSaveToPortfolio = async () => {
    if (!generatedAudioUrl || !musicTitle) {
      toast.error('Please generate music first before saving to portfolio');
      return;
    }

    setIsSaving(true);
    try {
      // Check if there's an active challenge
      let challengeId: string | undefined;
      try {
        const currentChallenge = localStorage.getItem('currentChallenge');
        if (currentChallenge) {
          const challenge = JSON.parse(currentChallenge);
          // Only link if the challenge type matches the content type
          if (challenge.type === 'music') {
            challengeId = challenge.id;
          }
        }
      } catch (error) {
        console.warn('Failed to parse current challenge from localStorage:', error);
      }

      const savedContent = await saveUserContent({
        type: 'music',
        title: musicTitle,
        preview_url: generatedAudioUrl,
        content_metadata: {
          description: musicDescription,
          style: selectedStyle,
          mood: selectedMood,
          generationId: generationId || undefined
        },
        challenge_id: challengeId
      });

      if (savedContent) {
        toast.success('Music saved to your portfolio!');

        // Trigger event to update dashboard counters
        window.dispatchEvent(new CustomEvent('contentSaved', {
          detail: {
            type: 'music',
            title: musicTitle
          }
        }));

        // Redirect to projects section after successful save
        setTimeout(() => {
          router.push("/my-projects");
        }, 1500);
      } else {
        throw new Error('Failed to save music to portfolio');
      }
    } catch (error) {
      console.error('Error saving music to portfolio:', error);
      toast.error('Failed to save music to portfolio. Please try again.');
    } finally {
      setIsSaving(false);
    }
  };

  const handleDownload = () => {
    if (!generatedAudioUrl) {
      toast.error('No music to download. Please generate music first.');
      return;
    }

    try {
      const a = document.createElement('a');
      a.href = generatedAudioUrl;
      a.download = `${musicTitle || 'music-composition'}.mp3`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      toast.success('Music composition downloaded!');
    } catch (error) {
      console.error('Error downloading music:', error);
      toast.error('Failed to download music. Please try again.');
    }
  };

  return {
    musicTitle,
    songTheme,
    setSongTheme,
    setMusicTitle,
    musicDescription,
    setMusicDescription,
    musicDuration,
    setMusicDuration,
    musicLyrics,
    selectedStyle,
    setSelectedStyle,
    selectedMood,
    setSelectedMood,
    isGeneratingDescription,
    isImprovingDescription,
    isGeneratingMusic,
    isGeneratingLyrics,
    generatedAudioUrl,
    isSaving,
    handleGenerateDescription,
    handleImproveDescription,
    // lyrics generation
    handleGenerateLyrics,
    handleGenerate,
    handleSaveToPortfolio,
    handleDownload
  };
};
