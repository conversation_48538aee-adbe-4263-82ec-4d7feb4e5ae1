import React from "react";
import AnnouncementBanner from "@/components/shared/AnnouncementBanner";
import CheckoutHeader from "@/components/checkout/CheckoutHeader";

interface CheckoutLayoutProps {
    children: React.ReactNode;
}

const CheckoutLayout = ({ children }: CheckoutLayoutProps) => {
    return (
        <div className="container mx-auto py-12 px-4 bg-gray-50">
            <AnnouncementBanner
                title="🌟 Founding Beta Special!"
                description="Join Little Spark and get 100% off for life with code ALPHA100"
                variant="lilac"
                allowDismiss={true}
                className="mb-8"
            />

            <div className="max-w-5xl mx-auto">
                <CheckoutHeader />
                {children}
            </div>
        </div>
    );
};

export default CheckoutLayout;
