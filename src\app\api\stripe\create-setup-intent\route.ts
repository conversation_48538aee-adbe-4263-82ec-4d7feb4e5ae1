import { NextRequest, NextResponse } from 'next/server';
import { createServerSupabaseClient } from '@/lib/supabase/server';
import { prisma } from '@/lib/prisma';
import Stripe from 'stripe';

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY!, {
  apiVersion: '2025-07-30.basil',
});

export async function POST(request: NextRequest) {
  try {
    const { planId, email, customerName, userId, promoCode } = await request.json();

    if (!planId || !email || !customerName || !userId) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      );
    }

    // Verify user authentication
    const supabase = await createServerSupabaseClient();
    const { data: { user } } = await supabase.auth.getUser();

    if (!user || user.id !== userId) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Get or create user profile
    let profile = await prisma.profile.findUnique({
      where: { id: userId }
    });

    if (!profile) {
      profile = await prisma.profile.create({
        data: {
          id: userId,
          email,
          full_name: customerName,
          trial_used: false,
        }
      });
    }

    // Validate promo code (only ALPHA100 for now)
    if (promoCode !== 'ALPHA100') {
      return NextResponse.json(
        { error: 'Invalid promo code' },
        { status: 400 }
      );
    }

    // Create or get Stripe customer
    let customer;
    if (profile.stripe_customer_id) {
      customer = await stripe.customers.retrieve(profile.stripe_customer_id);
    } else {
      customer = await stripe.customers.create({
        email,
        name: customerName,
        metadata: {
          userId,
          planId,
          promoCode,
        },
      });

      // Update profile with customer ID
      await prisma.profile.update({
        where: { id: userId },
        data: {
          stripe_customer_id: customer.id,
          updated_at: new Date()
        }
      });
    }

    // Create setup intent for future payments
    const setupIntent = await stripe.setupIntents.create({
      customer: customer.id,
      payment_method_types: ['card'],
      usage: 'off_session',
      metadata: {
        userId,
        planId,
        promoCode,
        purpose: 'promo_subscription_setup'
      },
    });

    return NextResponse.json({
      success: true,
      clientSecret: setupIntent.client_secret,
      customerId: customer.id,
      setupIntentId: setupIntent.id,
    });

  } catch (error) {
    console.error('Error creating setup intent:', error);
    return NextResponse.json(
      { error: 'Failed to create setup intent' },
      { status: 500 }
    );
  }
}
