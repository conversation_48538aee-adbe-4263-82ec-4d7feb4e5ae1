import { useState } from 'react';
import { useSubscriptionStatus } from './useSubscriptionStatus';
import { useSubscriptionManagement } from './useSubscriptionManagement';
import { SubscriptionPlan } from '@/components/subscription/SubscriptionPlans';
import { PaymentFormValues, GiftFormValues } from '@/components/subscription/payment-form/PaymentFormSchema';

export const useSubscription = () => {
  const [selectedPlan, setSelectedPlan] = useState<SubscriptionPlan | null>(null);
  const [paymentDialogOpen, setPaymentDialogOpen] = useState(false);
  const [isGift, setIsGift] = useState(false);
  const [recipientEmail, setRecipientEmail] = useState('');
  const [senderName, setSenderName] = useState('');
  const [giftMessage, setGiftMessage] = useState('');
  const [deliveryDate, setDeliveryDate] = useState<Date | null>(null);
  const [isProcessing, setIsProcessing] = useState(false);

  const {
    subscriptionStatus,
    hasActiveSubscription,
    showRenewalBanner,
    setShowRenewalBanner,
    refreshSubscriptionStatus,
    isLoading,
    accessResult,
    isTrialExpired,
    daysUntilExpiry
  } = useSubscriptionStatus();

  const { redeemGiftCode, handleManageSubscription } = useSubscriptionManagement();

  const handleSelectPlan = (plan: SubscriptionPlan) => {
    setSelectedPlan(plan);
    setIsGift(false);
    setPaymentDialogOpen(true);
  };

  const handleGiftPlan = (plan: SubscriptionPlan) => {
    setSelectedPlan(plan);
    setIsGift(true);
    setPaymentDialogOpen(true);
  };

  const handlePaymentSubmit = async (values: PaymentFormValues | GiftFormValues) => {
    setIsProcessing(true);
    
    try {
      // This would be replaced with actual payment processing logic
      console.log('Processing payment with values:', values);
      
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Success handling
      setPaymentDialogOpen(false);
      
      // Refresh subscription status after payment
      await refreshSubscriptionStatus();
      
      return { success: true };
    } catch (error) {
      console.error('Payment error:', error);
      return { success: false, error };
    } finally {
      setIsProcessing(false);
    }
  };

  return {
    // Status
    subscriptionStatus,
    hasActiveSubscription,
    showRenewalBanner,
    setShowRenewalBanner,
    refreshSubscriptionStatus,
    isLoading,
    accessResult,
    isTrialExpired,
    daysUntilExpiry,
    
    // Plan selection
    selectedPlan,
    handleSelectPlan,
    
    // Payment dialog
    paymentDialogOpen,
    setPaymentDialogOpen,
    isProcessing,
    
    // Gift subscription
    isGift,
    recipientEmail,
    setRecipientEmail,
    senderName,
    setSenderName,
    giftMessage,
    setGiftMessage,
    deliveryDate,
    setDeliveryDate,
    handleGiftPlan,
    
    // Payment handling
    handlePaymentSubmit,
    
    // Gift code redemption
    redeemGiftCode,
    
    // Subscription management
    handleManageSubscription,
  };
}; 