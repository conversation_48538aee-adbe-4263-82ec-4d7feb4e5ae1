import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { createServerSupabaseClient } from '@/lib/supabase/server';

// POST /api/user-content - Save user content to database
export async function POST(request: NextRequest) {
  try {
    console.log('🔄 [USER-CONTENT] Starting save request');

    // Get authenticated user
    const supabase = await createServerSupabaseClient();
    const { data: { user } } = await supabase.auth.getUser();

    if (!user) {
      console.log('❌ [USER-CONTENT] No authenticated user');
      return NextResponse.json(
        {
          success: false,
          error: 'Authentication required'
        },
        { status: 401 }
      );
    }

    console.log('✅ [USER-CONTENT] User authenticated:', user.id);

    const requestBody = await request.json();
    console.log('📝 [USER-CONTENT] Request body:', JSON.stringify(requestBody, null, 2));

    const {
      user_id,
      type,
      title,
      content_metadata,
      preview_url,
      challenge_id,
      content_hash
    } = requestBody;

    // Convert challenge_id to string if it's a number
    const challengeIdString = challenge_id ? String(challenge_id) : null;

    // Use authenticated user's ID if not provided or if it doesn't match
    const finalUserId = user_id && user_id === user.id ? user_id : user.id;

    // Validate required fields
    console.log('🔍 [USER-CONTENT] Validating fields - type:', type, 'title:', title);
    if (!type) {
      console.log('❌ [USER-CONTENT] Missing required field - type:', type);
      return NextResponse.json(
        {
          success: false,
          error: 'Type is required'
        },
        { status: 400 }
      );
    }

    // Validate content type
    const validTypes = ['story', 'art', 'music', 'chat', 'challenges', 'mindspark_history'];
    if (!validTypes.includes(type)) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Invalid content type' 
        },
        { status: 400 }
      );
    }

    // If challenge_id is provided, verify it exists (skip validation for CMS challenges)
    if (challengeIdString) {
      // CMS challenges have specific ID patterns (like 'cms-1', 'cms-2', etc.)
      // Skip database validation for CMS challenges
      const isCMSChallenge = challengeIdString.startsWith('cms-') || (/^[a-z]+$/.test(challengeIdString) && challengeIdString.length <= 3);

      if (!isCMSChallenge) {
        const challenge = await prisma.challenge.findUnique({
          where: { id: challengeIdString }
        });

        if (!challenge) {
          return NextResponse.json(
            {
              success: false,
              error: 'Invalid challenge ID'
            },
            { status: 400 }
          );
        }
      }
    }

    // Check for duplicate content if hash is provided
    if (content_hash) {
      const existingContent = await prisma.userContent.findFirst({
        where: {
          user_id: finalUserId,
          content_hash: content_hash
        }
      });

      if (existingContent) {
        return NextResponse.json({
          success: false,
          error: 'Content already saved to portfolio',
          duplicate: true,
          existingContent: {
            id: existingContent.id,
            title: existingContent.title,
            created_at: existingContent.created_at
          }
        }, { status: 409 });
      }
    }

    // Create the user content record
    console.log('💾 [USER-CONTENT] Creating record with data:', {
      user_id: finalUserId,
      type,
      title: title || 'Untitled',
      challenge_id: challengeIdString
    });

    const userContent = await prisma.userContent.create({
      data: {
        user_id: finalUserId,
        type,
        title: title || 'Untitled',
        content_metadata: content_metadata || {},
        preview_url,
        challenge_id: challengeIdString
        // Removed content_hash as it may not exist in database schema
      }
    });

    return NextResponse.json({
      success: true,
      message: 'Content saved successfully',
      content: {
        id: userContent.id,
        type: userContent.type,
        title: userContent.title,
        preview_url: userContent.preview_url,
        challenge_id: userContent.challenge_id,
        created_at: userContent.created_at
      }
    });

  } catch (error) {
    console.error('❌ [USER-CONTENT] Error saving user content:', error);

    // Log detailed error information
    if (error instanceof Error) {
      console.error('❌ [USER-CONTENT] Error message:', error.message);
      console.error('❌ [USER-CONTENT] Error stack:', error.stack);
    }

    // Handle specific database errors
    if (error instanceof Error) {
      // Handle unique constraint violations
      if (error.message.includes('unique constraint') || error.message.includes('duplicate')) {
        return NextResponse.json(
          {
            success: false,
            error: 'Content already exists',
            duplicate: true
          },
          { status: 409 }
        );
      }

      // Handle missing column errors
      if (error.message.includes('column') && error.message.includes('does not exist')) {
        console.error('❌ [USER-CONTENT] Database schema issue:', error.message);
        return NextResponse.json(
          {
            success: false,
            error: 'Database schema error'
          },
          { status: 500 }
        );
      }
    }

    return NextResponse.json(
      {
        success: false,
        error: 'Failed to save content'
      },
      { status: 500 }
    );
  }
}

// GET /api/user-content - Get user's content (portfolio)
export async function GET(request: NextRequest) {
  try {
    // Get authenticated user
    const supabase = await createServerSupabaseClient();
    const { data: { user } } = await supabase.auth.getUser();
    
    if (!user) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Authentication required' 
        },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const type = searchParams.get('type');
    const challengeId = searchParams.get('challenge_id');

    // Build where clause
    const whereClause: {
      user_id: string;
      type?: string | { in: string[] } | { not: string };
      challenge_id?: string;
    } = {
      user_id: user.id
    };

    if (type) {
      whereClause.type = type;
    } else {
      // Exclude portfolio shares and other non-project types
      whereClause.type = {
        in: ['story', 'art', 'music', 'chat', 'challenges', 'mindspark_history']
      };
    }

    if (challengeId) {
      whereClause.challenge_id = challengeId;
    }

    // Get user content
    const userContent = await prisma.userContent.findMany({
      where: whereClause,
      include: {
        challenge: {
          select: {
            id: true,
            title: true,
            type: true,
            difficulty: true
          }
        }
      },
      orderBy: {
        created_at: 'desc'
      }
    });

    return NextResponse.json({
      success: true,
      content: userContent
    });

  } catch (error) {
    console.error('Error fetching user content:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to fetch content' 
      },
      { status: 500 }
    );
  }
}
