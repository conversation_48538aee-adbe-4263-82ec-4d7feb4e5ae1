import React, { useState, useEffect, useCallback } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { useMindSparkAPI } from "./hooks/useMindSparkAPI";

interface QuestionHistoryProps {
    onBack: () => void;
}

interface HistoryEntry {
    id: string;
    question: {
        question: string;
        question_text: string;
        age_group: string;
        difficulty?: string;
        category?: string;
        is_ai_generated?: boolean;
    };
    answer: string;
    answered_at: string;
}

interface Stats {
    total_answered: number;
    recent_activity: number;
    current_streak: number;
    longest_streak: number;
}

const QuestionHistory: React.FC<QuestionHistoryProps> = ({ onBack }) => {
    const [history, setHistory] = useState<HistoryEntry[]>([]);
    const [stats, setStats] = useState<Stats | null>(null);
    const [isLoading, setIsLoading] = useState(true);
    const { fetchHistory } = useMindSparkAPI();

    const loadHistory = useCallback(async () => {
        setIsLoading(true);
        const historyData = await fetchHistory({ limit: 50 });
        if (historyData) {
            setHistory(historyData.history as unknown as HistoryEntry[]);
            setStats(historyData.stats);
        }
        setIsLoading(false);
    }, [fetchHistory]);

    useEffect(() => {
        loadHistory();
    }, [loadHistory]);

    if (isLoading) {
        return (
            <div className="space-y-4">
                <div className="flex items-center justify-between">
                    <h3 className="text-lg font-semibold">Your Question History</h3>
                    <Button onClick={onBack} variant="outline" size="sm">
                        Back to Questions
                    </Button>
                </div>
                <div className="text-center py-8">
                    <div className="inline-block h-8 w-8 animate-spin rounded-full border-4 border-solid border-spark-blue border-r-transparent"></div>
                    <p className="mt-2 text-gray-600">Loading history...</p>
                </div>
            </div>
        );
    }

    return (
        <div className="space-y-4">
            <div className="flex items-center justify-between">
                <h3 className="text-lg font-semibold">Your Question History</h3>
                <Button onClick={onBack} variant="outline" size="sm">
                    Back to Questions
                </Button>
            </div>

            {/* Stats Section */}
            {stats && (
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
                    <Card className="p-4 text-center">
                        <div className="text-2xl font-bold text-spark-blue">{stats.total_answered}</div>
                        <div className="text-sm text-gray-600">Total Answered</div>
                    </Card>
                    <Card className="p-4 text-center">
                        <div className="text-2xl font-bold text-spark-purple">{stats.recent_activity}</div>
                        <div className="text-sm text-gray-600">This Week</div>
                    </Card>
                    <Card className="p-4 text-center">
                        <div className="text-2xl font-bold text-green-600">{stats.current_streak}</div>
                        <div className="text-sm text-gray-600">Current Streak</div>
                    </Card>
                    <Card className="p-4 text-center">
                        <div className="text-2xl font-bold text-orange-600">{stats.longest_streak}</div>
                        <div className="text-sm text-gray-600">Longest Streak</div>
                    </Card>
                </div>
            )}

            {history.length === 0 ? (
                <p className="text-gray-500 text-center py-8">
                    You haven&apos;t answered any questions yet.
                </p>
            ) : (
                <div className="space-y-4">
                    {history.map((entry) => (
                        <Card key={entry.id} className="p-4">
                            <div className="space-y-3">
                                <div className="flex items-start justify-between">
                                    <h4 className="font-medium text-gray-900 flex-1">
                                        {entry.question.question}
                                    </h4>
                                    <div className="flex gap-2 text-xs ml-4">
                                        <span className="px-2 py-1 bg-spark-blue/20 text-spark-blue rounded-full">
                                            {entry.question.difficulty}
                                        </span>
                                        <span className="px-2 py-1 bg-spark-purple/20 text-spark-purple rounded-full">
                                            {entry.question.category}
                                        </span>
                                        {entry.question.is_ai_generated && (
                                            <span className="px-2 py-1 bg-green-100 text-green-700 rounded-full">
                                                🤖 AI
                                            </span>
                                        )}
                                    </div>
                                </div>

                                <div className="bg-blue-50 p-3 rounded-lg">
                                    <p className="text-sm font-medium text-blue-900 mb-1">Your Answer:</p>
                                    <p className="text-blue-800">{entry.answer}</p>
                                </div>

                                <div className="text-xs text-gray-500">
                                    Answered on {new Date(entry.answered_at).toLocaleDateString()} at {new Date(entry.answered_at).toLocaleTimeString()}
                                </div>
                            </div>
                        </Card>
                    ))}
                </div>
            )}
        </div>
    );
};

export default QuestionHistory;
