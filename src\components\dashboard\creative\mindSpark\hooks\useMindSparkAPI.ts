import { useState, useCallback } from 'react';
import { toast } from 'sonner';
import { Question, AgeGroup } from '../types';

interface MindSparkStats {
  total_answered: number;
  recent_activity: number;
  category_breakdown: Array<{
    category: string;
    age_group: string;
    answer_count: number;
  }>;
  current_streak: number;
  longest_streak: number;
}

interface MindSparkHistory {
  history: Array<{
    id: string;
    answer: string;
    answered_at: string;
    question: Question;
  }>;
  stats: MindSparkStats;
}

export const useMindSparkAPI = () => {
  const [isLoading, setIsLoading] = useState(false);
  const [isGenerating, setIsGenerating] = useState(false);

  // Fetch questions by age group
  const fetchQuestions = useCallback(async (
    age_group: AgeGroup,
    options: {
      category?: string;
      limit?: number;
      include_answered?: boolean;
    } = {}
  ): Promise<Question[]> => {
    setIsLoading(true);
    try {
      const params = new URLSearchParams({
        age_group,
        limit: (options.limit || 10).toString(),
        include_answered: (options.include_answered || false).toString()
      });

      if (options.category) {
        params.append('category', options.category);
      }

      const response = await fetch(`/api/mind-spark/questions?${params}`);
      const data = await response.json();

      if (!data.success) {
        throw new Error(data.error || 'Failed to fetch questions');
      }

      return data.questions;
    } catch (error) {
      console.error('Error fetching questions:', error);
      toast.error('Failed to load questions');
      return [];
    } finally {
      setIsLoading(false);
    }
  }, []);

  // Submit answer to a question
  const submitAnswer = useCallback(async (
    question_id: string,
    answer: string
  ): Promise<boolean> => {
    try {
      const response = await fetch('/api/mind-spark/questions', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          question_id,
          answer: answer.trim()
        })
      });

      const data = await response.json();

      if (!data.success) {
        if (response.status === 409) {
          toast.error('You have already answered this question');
        } else {
          toast.error(data.error || 'Failed to save answer');
        }
        return false;
      }

      // Show feedback based on correctness for MCQ questions
      if (data.answer.is_correct === true) {
        toast.success('Correct! Well done! 🎉');
      } else if (data.answer.is_correct === false) {
        toast.error('Incorrect. Don\'t worry, keep learning! 💪');
      } else {
        toast.success('Answer saved successfully! 🎉');
      }

      return true;
    } catch (error) {
      console.error('Error submitting answer:', error);
      toast.error('Failed to save answer');
      return false;
    }
  }, []);

  // Generate new question using AI
  const generateQuestion = useCallback(async (
    age_group: AgeGroup,
    category: string = 'general',
    difficulty: string = 'medium'
  ): Promise<Question | null> => {
    setIsGenerating(true);
    try {
      const response = await fetch('/api/mind-spark/generate', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          age_group,
          category,
          difficulty,
          question_type: 'mcq'
        })
      });

      const data = await response.json();

      if (!data.success) {
        throw new Error(data.error || 'Failed to generate question');
      }

      toast.success('New question generated! 🤖');
      return data.question;
    } catch (error) {
      console.error('Error generating question:', error);
      toast.error('Failed to generate new question');
      return null;
    } finally {
      setIsGenerating(false);
    }
  }, []);

  // Fetch user's question history and stats
  const fetchHistory = useCallback(async (
    options: {
      limit?: number;
      age_group?: AgeGroup;
    } = {}
  ): Promise<MindSparkHistory | null> => {
    try {
      const params = new URLSearchParams();
      
      if (options.limit) {
        params.append('limit', options.limit.toString());
      }
      
      if (options.age_group) {
        params.append('age_group', options.age_group);
      }

      const response = await fetch(`/api/mind-spark/history?${params}`);
      const data = await response.json();

      if (!data.success) {
        throw new Error(data.error || 'Failed to fetch history');
      }

      return data;
    } catch (error) {
      console.error('Error fetching history:', error);
      toast.error('Failed to load history');
      return null;
    }
  }, []);



  return {
    isLoading,
    isGenerating,
    fetchQuestions,
    submitAnswer,
    generateQuestion,
    fetchHistory
  };
};
