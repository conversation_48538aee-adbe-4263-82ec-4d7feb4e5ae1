"use client";
import React, { useEffect, useState, Suspense } from "react";
import MusicContainer from "@/components/music/MusicContainer";
import { useSubscriptionStatus } from '@/hooks/useSubscriptionStatus';
import { useRouter, useSearchParams } from 'next/navigation';
import { Loader2 } from 'lucide-react';

function MusicPageContent() {
    const { hasActiveSubscription, isLoading } = useSubscriptionStatus();
    const router = useRouter();
    const searchParams = useSearchParams();
    const [challengeData, setChallengeData] = useState(null);

    useEffect(() => {
        const challengeParam = searchParams.get('challenge');
        if (challengeParam) {
            try {
                const decoded = JSON.parse(decodeURIComponent(challengeParam));
                setChallengeData(decoded);
            } catch (error) {
                console.error('Error parsing challenge data:', error);
            }
        }
    }, [searchParams]);

    // Show loading screen while fetching subscription status
    if (isLoading) {
        return (
            <div className="min-h-screen bg-gradient-to-b from-white to-gray-50 flex items-center justify-center">
                <div className="text-center">
                    <Loader2 className="h-12 w-12 text-[#FF6B35] animate-spin mx-auto mb-4" />
                    <p className="text-gray-600 text-lg">Loading Music Composer...</p>
                </div>
            </div>
        );
    }

    return (
        <div className="relative">
            <main className="min-h-screen bg-gradient-to-b from-white to-gray-50">
                <div className="container mx-auto px-4 py-4">
                    <MusicContainer challengeData={challengeData || undefined} />
                </div>
            </main>
            {!hasActiveSubscription && (
                <div
                    className="absolute inset-0 bg-black bg-opacity-50 cursor-pointer flex items-center justify-center"
                    onClick={() => router.push('/pricing')}
                >
                    <div className="bg-white rounded-lg p-8 max-w-md mx-4 text-center">
                        <h3 className="text-xl font-bold mb-2">Upgrade to Access Music Composer</h3>
                        <p className="text-gray-600 mb-4">
                            Create theme songs for your stories and characters. Unlock this feature with a subscription.
                        </p>
                        <button
                            onClick={() => router.push('/pricing')}
                            className="bg-[#FF6B35] text-white px-6 py-2 rounded-lg font-semibold hover:bg-[#e55a2b] transition-colors"
                        >
                            View Plans
                        </button>
                    </div>
                </div>
            )}
        </div>
    );
}

export default function MusicPage() {
    return (
        <Suspense fallback={<div>Loading...</div>}>
            <MusicPageContent />
        </Suspense>
    );
}
