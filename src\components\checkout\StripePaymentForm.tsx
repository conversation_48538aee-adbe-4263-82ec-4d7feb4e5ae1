"use client";

import React, { useState, useEffect, useCallback } from "react";
import {
    useStripe,
    useElements,
    PaymentElement,
    Elements,
} from "@stripe/react-stripe-js";
import { getStripe } from "@/lib/stripe";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { toast } from "sonner";
import { useAuth } from "@/hooks/useAuth";
import { Play, Shield, Sparkles, CreditCard } from "lucide-react";

interface StripePaymentFormProps {
    planId: string;
    onSuccess: () => void;
    onError: (error: string) => void;
    userEmail?: string;
    userName?: string;
    autoInit?: boolean;
}

// Payment form component that uses Stripe hooks - only rendered when we have clientSecret
const PaymentFormContent = ({
    email,
    customerName,
    isSetupIntent,
    isTrialSubscription,
    amount,
    planId,
    clientSecret,
    onSuccess,
    onError,
}: {
    email: string;
    customerName: string;
    isSetupIntent?: boolean;
    isTrialSubscription?: boolean;
    amount?: number;
    planId: string;
    clientSecret?: string;
    onSuccess: () => void;
    onError: (error: string) => void;
}) => {
    const { user } = useAuth();
    const stripe = useStripe();
    const elements = useElements();
    const [isLoading, setIsLoading] = useState(false);
    const [promoCode, setPromoCode] = useState("");
    const [promoApplied, setPromoApplied] = useState(false);
    const [discountAmount, setDiscountAmount] = useState(0);
    const [originalAmount, setOriginalAmount] = useState(amount || 0);
    const [finalAmount, setFinalAmount] = useState(amount || 0);

    // Initialize amounts when component mounts
    useEffect(() => {
        if (amount) {
            setOriginalAmount(amount);
            setFinalAmount(amount);
        }
    }, [amount]);

    // Apply promo code function
    const applyPromoCode = async () => {
        if (!promoCode.trim()) {
            toast.error("Please enter a promo code");
            return;
        }

        if (promoCode === "ALPHA100") {
            setPromoApplied(true);
            setDiscountAmount(originalAmount);
            setFinalAmount(0);
            toast.success("🎉 ALPHA100 Applied - 100% Discount!");
        } else {
            toast.error("Invalid promo code");
            setPromoApplied(false);
            setDiscountAmount(0);
            setFinalAmount(originalAmount);
        }
    };



    const handleSubmit = async (event: React.FormEvent) => {
        event.preventDefault();

        if (!stripe || !elements) {
            return;
        }

        // For zero-cost payment with promo code, we still need to collect card details
        // for future billing, so we'll process it as a setup intent
        if (finalAmount === 0 && promoApplied) {
            setIsLoading(true);
            try {
                // Since we're using setup intents for everything now, confirm the setup
                const { error, setupIntent } = await stripe.confirmSetup({
                    elements,
                    confirmParams: {
                        return_url: `${window.location.origin}/thank-you`,
                    },
                    redirect: "if_required",
                });

                if (error) {
                    onError(error.message || "Setup failed");
                    toast.error(error.message || "Failed to save payment method");
                } else if (setupIntent && setupIntent.status === "succeeded") {
                    // Activate the promo subscription
                    const activateResponse = await fetch("/api/stripe/activate-promo-subscription", {
                        method: "POST",
                        headers: {
                            "Content-Type": "application/json",
                        },
                        body: JSON.stringify({
                            planId,
                            email,
                            customerName,
                            userId: user?.id,
                            promoCode: promoCode.trim(),
                            setupIntentId: setupIntent.id,
                        }),
                    });

                    const activateData = await activateResponse.json();

                    if (activateData.error) {
                        throw new Error(activateData.error);
                    }

                    toast.success("🎉 Subscription Activated FREE!", {
                        description: "Card saved for future billing. Welcome to Little Spark!",
                        duration: 3000,
                    });

                    setTimeout(() => {
                        onSuccess();
                    }, 2000);
                }
            } catch (error) {
                console.error("Error processing promo subscription:", error);
                toast.error(error instanceof Error ? error.message : "Failed to activate subscription");
            } finally {
                setIsLoading(false);
            }
            return;
        }

        setIsLoading(true);

        try {
            if (isSetupIntent) {
                // Handle setup intent for trial subscriptions
                const { error, setupIntent } = await stripe.confirmSetup({
                    elements,
                    confirmParams: {
                        return_url: `${window.location.origin}/thank-you`,
                    },
                    redirect: "if_required",
                });

                if (error) {
                    onError(error.message || "Setup failed");
                    toast.error(error.message || "Setup failed");
                } else if (setupIntent && setupIntent.status === "succeeded") {
                    toast.success("Trial started successfully!");
                    onSuccess();
                }
            } else {
                // Handle payment intent for direct payments
                const { error, paymentIntent } = await stripe.confirmPayment({
                    elements,
                    confirmParams: {
                        return_url: `${window.location.origin}/thank-you`,
                        receipt_email: email,
                    },
                    redirect: "if_required",
                });

                if (error) {
                    onError(error.message || "Payment failed");
                    toast.error(error.message || "Payment failed");
                } else if (
                    paymentIntent &&
                    paymentIntent.status === "succeeded"
                ) {
                    // For direct payments, create subscription after payment success
                    try {
                        const response = await fetch("/api/stripe/create-subscription-after-payment", {
                            method: "POST",
                            headers: {
                                "Content-Type": "application/json",
                            },
                            body: JSON.stringify({
                                paymentIntentId: paymentIntent.id,
                                planId: planId,
                                userId: user?.id,
                            }),
                        });

                        const data = await response.json();

                        if (data.error) {
                            throw new Error(data.error);
                        }

                        toast.success("Payment successful! Subscription activated.");
                        onSuccess();
                    } catch (subscriptionError) {
                        console.error("Error creating subscription:", subscriptionError);
                        toast.error("Payment successful but subscription creation failed. Please contact support.");
                        onError("Subscription creation failed");
                    }
                }
            }
        } catch (error) {
            console.error("Payment/Setup error:", error);
            onError("An unexpected error occurred");
            toast.error("An unexpected error occurred");
        } finally {
            setIsLoading(false);
        }
    };

    return (
        <form onSubmit={handleSubmit} className="space-y-6">
            <div className="p-4 bg-gray-50 rounded-lg">
                <p className="text-sm text-gray-600 mb-2">
                    <strong>Email:</strong> {email}
                </p>
                <p className="text-sm text-gray-600">
                    <strong>Name:</strong> {customerName}
                </p>
            </div>

            {/* Promo Code Section */}
            <div className="space-y-4">
                <div className="border rounded-lg p-4">
                    <h3 className="text-sm font-medium text-gray-700 mb-3">
                        Have a Promo Code? 🎁
                    </h3>
                    <div className="flex gap-2">
                        <Input
                            type="text"
                            value={promoCode}
                            onChange={(e) => setPromoCode(e.target.value.toUpperCase())}
                            placeholder="Enter promo code (e.g., ALPHA100)"
                            className="flex-1"
                        />
                        <Button
                            type="button"
                            onClick={applyPromoCode}
                            disabled={!promoCode.trim() || promoApplied}
                            variant="outline"
                            className="px-4"
                        >
                            Apply
                        </Button>
                    </div>

                    {promoApplied && promoCode === "ALPHA100" && (
                        <div className="mt-3 p-3 bg-green-50 border border-green-200 rounded-lg">
                            <p className="text-sm text-green-800 font-medium">
                                🎉 ALPHA100 Applied - 100% Discount!
                            </p>
                            <p className="text-xs text-green-600 mt-1">
                                Your subscription will be activated for FREE
                            </p>
                        </div>
                    )}

                    {!promoApplied && (
                        <p className="text-xs text-gray-500 mt-2">
                            💡 Try <span className="font-mono bg-gray-100 px-1 rounded">ALPHA100</span> for a free subscription
                        </p>
                    )}
                </div>

                {/* Price Summary */}
                <div className="border rounded-lg p-4 bg-gray-50">
                    <div className="space-y-2">
                        <div className="flex justify-between text-sm">
                            <span>Original Price:</span>
                            <span>${originalAmount}</span>
                        </div>
                        {promoApplied && (
                            <div className="flex justify-between text-sm text-green-600">
                                <span>Discount ({promoCode}):</span>
                                <span>-${discountAmount}</span>
                            </div>
                        )}
                        <div className="border-t pt-2 flex justify-between font-bold">
                            <span>Total:</span>
                            <span className={finalAmount === 0 ? "text-green-600" : ""}>
                                ${finalAmount}
                                {finalAmount === 0 && " (FREE!)"}
                            </span>
                        </div>
                    </div>
                </div>
            </div>

            {/* Always show payment element, even for free subscriptions (for future billing) */}
            <div className="stripe-payment-element">
                {finalAmount === 0 && promoApplied && (
                    <div className="mb-4 p-3 bg-blue-50 border border-blue-200 rounded-lg">
                        <p className="text-sm text-blue-800">
                            💳 <strong>Card Required for Future Billing</strong>
                        </p>
                        <p className="text-xs text-blue-700 mt-1">
                            Your card will be saved for automatic billing when your free period ends. No charge today!
                        </p>
                    </div>
                )}
                <PaymentElement
                    options={{
                        layout: "tabs",
                        paymentMethodOrder: ["card", "apple_pay", "google_pay"],
                    }}
                />
            </div>

            {isTrialSubscription ? (
                <div className="p-4 bg-blue-50 rounded-lg">
                    <p className="text-sm text-blue-800 font-medium mb-1">
                        🎉 Your 7-Day Free Trial
                    </p>
                    <p className="text-xs text-blue-700">
                        Card required for trial. You won&apos;t be charged until your trial ends. Cancel anytime during the trial period.
                    </p>
                </div>
            ) : (
                <div className="p-4 bg-green-50 rounded-lg">
                    <p className="text-sm text-green-800 font-medium mb-1">
                        💳 Direct Payment - ${amount}
                    </p>
                    <p className="text-xs text-green-700">
                        You&apos;ll be charged immediately and your subscription will start right away.
                    </p>
                </div>
            )}

            <button
                type="submit"
                disabled={isLoading || (finalAmount > 0 && (!stripe || !elements))}
                className="relative w-full h-14 px-8 py-3 rounded-xl text-white text-base font-bold
                         bg-gradient-to-r from-green-500 to-emerald-600
                         hover:from-green-600 hover:to-emerald-700
                         transform hover:scale-[1.02] transition-all duration-200
                         shadow-lg hover:shadow-xl
                         disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none
                         flex items-center justify-center gap-3
                         group overflow-hidden"
            >
                <div className="absolute inset-0 bg-gradient-to-r from-white/0 to-white/20 transform translate-x-[-100%] group-hover:translate-x-[100%] transition-transform duration-700"></div>
                {finalAmount === 0 && promoApplied ? (
                    <>
                        <Sparkles className="h-5 w-5 relative z-10" />
                        <span className="relative z-10">
                            {isLoading ? "Activating..." : "Activate FREE Subscription"}
                        </span>
                        <Play className="h-5 w-5 relative z-10 group-hover:scale-110 transition-transform duration-200" />
                    </>
                ) : isTrialSubscription ? (
                    <>
                        <Sparkles className="h-5 w-5 relative z-10" />
                        <span className="relative z-10">
                            {isLoading ? "Processing..." : "Start Free Trial"}
                        </span>
                        <Play className="h-5 w-5 relative z-10 group-hover:scale-110 transition-transform duration-200" />
                    </>
                ) : (
                    <>
                        <CreditCard className="h-5 w-5 relative z-10" />
                        <span className="relative z-10">
                            {isLoading ? "Processing..." : `Pay $${finalAmount}`}
                        </span>
                        <Play className="h-5 w-5 relative z-10 group-hover:scale-110 transition-transform duration-200" />
                    </>
                )}
            </button>

            <div className="flex items-center justify-center gap-2 text-xs text-gray-500">
                <Shield className="h-4 w-4" />
                <span>Your payment is secured by Stripe. We don&apos;t store your payment information.</span>
            </div>
        </form>
    );
};

const StripePaymentForm = ({
    planId,
    onSuccess,
    onError,
    userEmail = "",
    userName = "",
    // autoInit = false,
}: StripePaymentFormProps) => {
    const { user } = useAuth();
    const [isLoading, setIsLoading] = useState(false);
    const [email, setEmail] = useState("");
    const [customerName, setCustomerName] = useState("");

    const [clientSecret, setClientSecret] = useState<string | null>(null);
    const [isSetupIntent, setIsSetupIntent] = useState(false);
    const [isTrialSubscription, setIsTrialSubscription] = useState(false);
    const [amount, setAmount] = useState<number | undefined>();
    const [testMode, setTestMode] = useState(false);

    const handleInitializePayment = useCallback(async () => {
        if (!email.trim()) {
            toast.error("Please enter your email address");
            return;
        }

        if (!customerName.trim()) {
            toast.error("Please enter your name");
            return;
        }

        setIsLoading(true);

        try {
            const response = await fetch("/api/stripe/create-payment-intent", {
                method: "POST",
                headers: {
                    "Content-Type": "application/json",
                },
                body: JSON.stringify({
                    planId,
                    email,
                    customerName,
                    userId: user?.id,
                    // promoCode will be applied later on payment page
                }),
            });

            const data = await response.json();

            if (data.error) {
                throw new Error(data.error);
            }

            // Handle zero-cost payment (promo code success)
            if (data.zeroCostPayment) {
                toast.success(data.message || "Subscription activated with promo code!");
                onSuccess();
                return;
            }

            setClientSecret(data.clientSecret);
            setIsSetupIntent(!!data.setupIntent);
            setIsTrialSubscription(!!data.isTrialSubscription);
            setAmount(data.amount);
            setTestMode(!!data.testMode);

            if (data.testMode) {
                toast.success("Test mode: Payment form loaded!");
            } else {
                toast.success("Payment form loaded successfully!");
            }
        } catch (error) {
            console.error("Error initializing payment:", error);
            onError(
                error instanceof Error
                    ? error.message
                    : "Failed to initialize payment"
            );
        } finally {
            setIsLoading(false);
        }
    }, [planId, email, customerName, user?.id, onError, onSuccess]);

    // Prepopulate user data
    useEffect(() => {
        if (userEmail) {
            setEmail(userEmail);
        } else if (user?.email) {
            setEmail(user.email);
        }

        if (userName) {
            setCustomerName(userName);
        }
    }, [userEmail, userName, user]);

    // Auto-initialize payment when email and name are available (skip the form step)
    useEffect(() => {
        if (email && customerName && !clientSecret && !isLoading) {
            handleInitializePayment();
        }
    }, [email, customerName, clientSecret, isLoading, handleInitializePayment]);

    // Show loading spinner while initializing payment
    if (!clientSecret && (email && customerName)) {
        return (
            <div className="flex flex-col items-center justify-center py-20 space-y-4">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-littlespark-teal"></div>
                <p className="text-gray-600">Setting up your payment form...</p>
            </div>
        );
    }

    // Only show customer info form if we don't have email or name
    if (!clientSecret && (!email || !customerName)) {
        return (
            <div className="space-y-4">
                <div>
                    <label
                        htmlFor="email"
                        className="block text-sm font-medium text-gray-700 mb-2"
                    >
                        Email Address
                    </label>
                    <Input
                        id="email"
                        type="email"
                        value={email}
                        onChange={(e) => setEmail(e.target.value)}
                        placeholder="<EMAIL>"
                        required
                        className="w-full"
                    />
                </div>

                <div>
                    <label
                        htmlFor="name"
                        className="block text-sm font-medium text-gray-700 mb-2"
                    >
                        Full Name
                    </label>
                    <Input
                        id="name"
                        type="text"
                        value={customerName}
                        onChange={(e) => setCustomerName(e.target.value)}
                        placeholder="John Doe"
                        required
                        className="w-full"
                    />
                </div>



                {/* TRIAL SYSTEM COMMENTED OUT - Direct purchase only */}
                {/*
                <div className="p-4 bg-green-50 rounded-lg">
                    <p className="text-sm text-green-800 font-medium mb-1">
                        🎉 7-Day Free Trial Included
                    </p>
                    <p className="text-xs text-green-700">
                        Start your trial now! You&apos;ll only be charged after
                        the trial period ends.
                    </p>
                </div>
                */}

                <div className="p-4 bg-blue-50 rounded-lg">
                    <p className="text-sm text-blue-800 font-medium mb-1">
                        💳 Direct Purchase
                    </p>
                    <p className="text-xs text-blue-700">
                        Get instant access to all features. Promo codes accepted on next step!
                    </p>
                </div>

                <Button
                    onClick={handleInitializePayment}
                    disabled={isLoading || !email.trim() || !customerName.trim()}
                    className="w-full h-12 text-base font-bold"
                >
                    {isLoading ? (
                        <>
                            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                            Setting up payment...
                        </>
                    ) : (
                        <>
                            Continue to Payment
                            <CreditCard className="h-4 w-4 ml-2" />
                        </>
                    )}
                </Button>
            </div>
        );
    }

    // Show test mode message if in test mode
    if (testMode) {
        return (
            <div className="space-y-4">
                <div className="p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
                    <p className="text-sm text-yellow-800 font-medium mb-1">
                        🚧 Development Mode
                    </p>
                    <p className="text-xs text-yellow-700">
                        Stripe is not fully configured. This is a test
                        environment.
                    </p>
                </div>
                <Button
                    onClick={onSuccess}
                    className="w-full bg-littlespark-primary hover:bg-littlespark-primary-hover h-12 text-base font-bold"
                >
                    Continue (Test Mode)
                </Button>
            </div>
        );
    }

    // Show payment form wrapped in Elements when we have clientSecret
    return (
        <Elements
            stripe={getStripe()}
            options={{
                clientSecret: clientSecret || undefined,
            }}
        >
            <PaymentFormContent
                email={email}
                customerName={customerName}
                isSetupIntent={isSetupIntent}
                isTrialSubscription={isTrialSubscription}
                amount={amount}
                planId={planId}
                clientSecret={clientSecret || undefined}
                onSuccess={onSuccess}
                onError={onError}
            />
        </Elements>
    );
};

export default StripePaymentForm;
