import { loadStripe } from '@stripe/stripe-js';
import type { Stripe } from '@stripe/stripe-js';

// Client-side Stripe
let stripePromise: Promise<Stripe | null>;

export const getStripe = () => {
  if (!stripePromise) {
    const publishableKey = process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY;
    if (!publishableKey) {
      console.warn('NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY is not set');
      return Promise.resolve(null);
    }
    stripePromise = loadStripe(publishableKey);
  }
  return stripePromise;
};

// Server-side Stripe (for API routes)
import StripeNode from 'stripe';

const secretKey = process.env.STRIPE_SECRET_KEY;
export const stripe = secretKey ? new StripeNode(secretKey, {
  apiVersion: '2025-07-30.basil',
}) : null;

// Type for subscription with period information
export interface StripeSubscriptionWithPeriods extends StripeNode.Subscription {
  current_period_start: number;
  current_period_end: number;
}

// Plan configuration - using proper Stripe intervals
export const STRIPE_PLANS = {
  'monthly-tier': {
    priceId: process.env.STRIPE_MONTHLY_PRICE_ID!,
    amount: 1499, // $14.99 in cents
    interval: 'month' as const,
    intervalCount: 1,
    billingCycle: 'month',
  },
  'quarterly-tier': {
    priceId: process.env.STRIPE_QUARTERLY_PRICE_ID!,
    amount: 3597, // $35.97 in cents (3 months)
    interval: 'month' as const,
    intervalCount: 3,
    billingCycle: 'quarter',
  },
  'annual-tier': {
    priceId: process.env.STRIPE_ANNUAL_PRICE_ID!,
    amount: 11999, // $119.99 in cents
    interval: 'year' as const,
    intervalCount: 1,
    billingCycle: 'year',
  },
} as const;

export type PlanId = keyof typeof STRIPE_PLANS;

// Utility functions for subscription billing
export function getPlanIdFromPriceId(priceId?: string): string | null {
  if (!priceId) return null;
  
  // Map Stripe price IDs to plan IDs based on environment variables
  const priceIdMapping: Record<string, string> = {
    [process.env.STRIPE_MONTHLY_PRICE_ID || '']: 'monthly-tier',
    [process.env.STRIPE_QUARTERLY_PRICE_ID || '']: 'quarterly-tier',
    [process.env.STRIPE_ANNUAL_PRICE_ID || '']: 'annual-tier'
  };
  
  return priceIdMapping[priceId] || null;
}

export function getBillingCycleFromPlanId(planId: string): string {
  const plan = STRIPE_PLANS[planId as PlanId];
  return plan?.billingCycle || 'month';
}

export function getPlanDisplayName(planId: string): string {
  const displayNames: Record<string, string> = {
    'monthly-tier': 'Monthly Plan',
    'quarterly-tier': 'Quarterly Plan', 
    'annual-tier': 'Annual Plan'
  };
  return displayNames[planId] || 'Unknown Plan';
}

// Calculate subscription end date based on plan type
export function calculateSubscriptionEndDate(startDate: Date, planId: string): Date {
  const endDate = new Date(startDate);
  
  switch (planId) {
    case 'monthly-tier':
      endDate.setMonth(endDate.getMonth() + 1);
      break;
    case 'quarterly-tier':
      endDate.setMonth(endDate.getMonth() + 3);
      break;
    case 'annual-tier':
      endDate.setFullYear(endDate.getFullYear() + 1);
      break;
    default:
      endDate.setMonth(endDate.getMonth() + 1); // Default to monthly
      break;
  }
  
  return endDate;
}

// Add months to Unix timestamp (used for quarterly calculations)
export function addMonthsToUnix(startSeconds: number, months: number): number {
  const startDate = new Date(startSeconds * 1000);
  const endDate = new Date(startDate);
  endDate.setMonth(endDate.getMonth() + months);
  return Math.floor(endDate.getTime() / 1000);
}

// Calculate correct billing periods for any plan type
export function calculateBillingPeriod(
  subscription: StripeSubscriptionWithPeriods,
  planId: string | null
): { periodStart: number; periodEnd: number } {
  let periodStart = subscription.current_period_start;
  let periodEnd = subscription.current_period_end;
  
  // If we have both periods from Stripe, use them unless they're incorrect for quarterly
  if (periodStart && periodEnd && planId === 'quarterly-tier') {
    const periodLengthDays = (periodEnd - periodStart) / (24 * 60 * 60);
    
    // If the period is not approximately 3 months (80-100 days), recalculate
    if (periodLengthDays < 80 || periodLengthDays > 100) {
      console.log(`🔧 Recalculating quarterly period: current length is ${periodLengthDays} days`);
      periodEnd = addMonthsToUnix(periodStart, 3);
    }
  }
  
  // If we don't have periods, calculate them based on trial or subscription start
  if (!periodStart || !periodEnd) {
    const startTime = subscription.trial_start || subscription.created;
    periodStart = subscription.trial_end || startTime;
    
    switch (planId) {
      case 'monthly-tier':
        periodEnd = addMonthsToUnix(periodStart, 1);
        break;
      case 'quarterly-tier':
        periodEnd = addMonthsToUnix(periodStart, 3);
        break;
      case 'annual-tier':
        periodEnd = addMonthsToUnix(periodStart, 12);
        break;
      default:
        periodEnd = addMonthsToUnix(periodStart, 1);
        break;
    }
  }
  
  return { periodStart, periodEnd };
} 