import React, { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Send } from "lucide-react";
import FeedbackForm from "@/components/feedback/FeedbackForm";

const TestimonialManager = () => {
    const [showFeedbackDialog, setShowFeedbackDialog] = useState(false);

    return (
        <div className="space-y-6">
            <div className="flex justify-between items-center">
                <h2 className="text-2xl font-bold">Your Feedback</h2>
                <Button
                    onClick={() => setShowFeedbackDialog(true)}
                    className="gap-2"
                >
                    <Send className="h-4 w-4" />
                    Share Your Experience
                </Button>
            </div>

            <div className="bg-spark-background p-6 rounded-lg">
                <p className="text-gray-700">
                    We value your feedback! Share your experience with Little
                    Spark to help us improve and inspire others. Your
                    feedback will be sent directly to our team.
                </p>
            </div>

            <FeedbackForm
                open={showFeedbackDialog}
                onOpenChange={setShowFeedbackDialog}
            />
        </div>
    );
};

export default TestimonialManager;
