'use client';

import { useState } from 'react';

interface SyncStats {
  cmsChallenge: number;
  mainAppCmsChallenge: number;
  mainAppTotalChallenge: number;
  lastSync: string;
}

export default function TestSyncPage() {
  const [stats, setStats] = useState<SyncStats | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [message, setMessage] = useState<string>('');

  const fetchStats = async () => {
    try {
      setMessage('Fetching stats from CMS...');
      const response = await fetch('http://localhost:3001/api/sync/challenges');
      const data = await response.json();
      
      if (data.success) {
        setStats(data.stats);
        setMessage('✅ Stats loaded successfully!');
      } else {
        setMessage('❌ Failed to load stats');
      }
    } catch (error) {
      setMessage(`❌ Error: ${error}`);
    }
  };

  const addTestData = async () => {
    setIsLoading(true);
    try {
      setMessage('📝 Adding sample challenges to CMS...');
      const response = await fetch('http://localhost:3001/api/test-data', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      const data = await response.json();

      if (data.success) {
        setMessage(`✅ Test data added! ${data.stats.created} challenges created, ${data.stats.skipped} skipped.`);
        await fetchStats(); // Refresh stats
      } else {
        setMessage(`❌ Failed to add test data: ${data.error}`);
      }
    } catch (error) {
      setMessage(`❌ Test data error: ${error}`);
    } finally {
      setIsLoading(false);
    }
  };

  const syncChallenges = async () => {
    setIsLoading(true);
    try {
      setMessage('🔄 Syncing challenges...');
      const response = await fetch('http://localhost:3001/api/sync/challenges', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      const data = await response.json();

      if (data.success) {
        setMessage(`✅ Sync completed! ${data.stats.synced} challenges synced.`);
        await fetchStats(); // Refresh stats
      } else {
        setMessage(`❌ Sync failed: ${data.error}`);
      }
    } catch (error) {
      setMessage(`❌ Sync error: ${error}`);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 p-8">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-3xl font-bold text-gray-800 mb-8">
          🧪 Sync System Test Page
        </h1>

        {/* Message Display */}
        {message && (
          <div className="mb-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
            <p className="text-blue-800">{message}</p>
          </div>
        )}

        {/* Stats Display */}
        {stats && (
          <div className="mb-8 bg-white rounded-lg shadow-md p-6">
            <h2 className="text-xl font-semibold mb-4">📊 Current Stats</h2>
            <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
              <div className="bg-blue-50 p-4 rounded-lg text-center">
                <div className="text-2xl font-bold text-blue-600">{stats.cmsChallenge}</div>
                <div className="text-sm text-blue-800">CMS Challenges</div>
              </div>
              <div className="bg-green-50 p-4 rounded-lg text-center">
                <div className="text-2xl font-bold text-green-600">{stats.mainAppCmsChallenge}</div>
                <div className="text-sm text-green-800">Synced to Main App</div>
              </div>
              <div className="bg-purple-50 p-4 rounded-lg text-center">
                <div className="text-2xl font-bold text-purple-600">{stats.mainAppTotalChallenge}</div>
                <div className="text-sm text-purple-800">Total in Main App</div>
              </div>
            </div>
            <p className="mt-4 text-sm text-gray-600">
              <strong>Last Sync:</strong> {stats.lastSync}
            </p>
          </div>
        )}

        {/* Action Buttons */}
        <div className="space-y-4">
          <button
            type="button"
            onClick={addTestData}
            disabled={isLoading}
            className={`w-full md:w-auto px-6 py-3 rounded-lg transition-colors ${
              isLoading
                ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
                : 'bg-purple-600 text-white hover:bg-purple-700'
            }`}
          >
            {isLoading ? '📝 Adding...' : '📝 Add Sample Challenges to CMS'}
          </button>

          <button
            type="button"
            onClick={fetchStats}
            className="w-full md:w-auto px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            📊 Fetch Stats from CMS
          </button>

          <button
            type="button"
            onClick={syncChallenges}
            disabled={isLoading}
            className={`w-full md:w-auto px-6 py-3 rounded-lg transition-colors ${
              isLoading
                ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
                : 'bg-green-600 text-white hover:bg-green-700'
            }`}
          >
            {isLoading ? '🔄 Syncing...' : '🔄 Sync Challenges'}
          </button>
        </div>

        {/* Instructions */}
        <div className="mt-8 bg-yellow-50 border border-yellow-200 rounded-lg p-6">
          <h3 className="text-lg font-semibold text-yellow-800 mb-3">
            📝 Test Instructions
          </h3>
          <ol className="list-decimal list-inside text-sm text-yellow-800 space-y-2">
            <li>Click &quot;Add Sample Challenges to CMS&quot; to create test data in CMS</li>
            <li>Click &quot;Fetch Stats from CMS&quot; to load current statistics</li>
            <li>Click &quot;Sync Challenges&quot; to sync challenges from CMS to main app</li>
            <li>Check the message area for success/error feedback</li>
            <li>Refresh stats to see updated numbers</li>
          </ol>
        </div>

        {/* API Endpoints Info */}
        <div className="mt-6 bg-gray-50 border border-gray-200 rounded-lg p-6">
          <h3 className="text-lg font-semibold text-gray-800 mb-3">
            🔗 API Endpoints
          </h3>
          <div className="text-sm text-gray-600 space-y-2">
            <p><strong>CMS Stats:</strong> GET http://localhost:3001/api/sync/challenges</p>
            <p><strong>CMS Sync:</strong> POST http://localhost:3001/api/sync/challenges</p>
            <p><strong>Main App Admin:</strong> <a href="/admin" className="text-blue-600 hover:underline">/admin</a></p>
            <p><strong>Main App Sync:</strong> <a href="/admin/sync" className="text-blue-600 hover:underline">/admin/sync</a></p>
          </div>
        </div>

        {/* Navigation */}
        <div className="mt-8 text-center">
          <a
            href="/admin"
            className="inline-block px-6 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors"
          >
            🔐 Go to Admin Panel
          </a>
        </div>
      </div>
    </div>
  );
}
