import { NextRequest, NextResponse } from 'next/server';
import { createServerSupabaseClient } from '@/lib/supabase/server';

export async function POST(request: NextRequest) {
  try {
    const { email } = await request.json();

    if (!email) {
      return NextResponse.json(
        { error: 'Email is required' },
        { status: 400 }
      );
    }

    const supabase = await createServerSupabaseClient();

    // First, check if user exists by trying to get user data
    // We'll use the admin client to check if user exists
    const { data: existingUsers, error: listError } = await supabase.auth.admin.listUsers({
      page: 1,
      perPage: 1000 // This is a limitation, but for most apps this should be fine
    });

    if (listError) {
      console.error('Error checking user existence:', listError);
      // If we can't check, proceed with the original flow
    } else {
      // Check if user with this email exists
      const userExists = existingUsers?.users?.some(user => user.email === email);

      if (!userExists) {
        return NextResponse.json(
          {
            error: 'Account not found. Please create an account first or check your email address.',
            userNotFound: true
          },
          { status: 404 }
        );
      }
    }

    // Sign in with magic link
    const { error } = await supabase.auth.signInWithOtp({
      email,
      options: {
        shouldCreateUser: false,
        emailRedirectTo: `${process.env.NEXT_PUBLIC_SITE_URL}/auth/callback`,
      }
    });

    if (error) {
      console.error('Signin error:', error);

      // Handle specific error cases
      if (error.message.includes('User not found') || error.message.includes('Invalid login credentials')) {
        return NextResponse.json(
          {
            error: 'Account not found. Please create an account first or check your email address.',
            userNotFound: true
          },
          { status: 404 }
        );
      }

      return NextResponse.json(
        { error: error.message },
        { status: 400 }
      );
    }

    return NextResponse.json({
      message: 'Magic link sent! Check your email to sign in.',
      success: true
    });

  } catch (error) {
    console.error('Signin API error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}