import { NextRequest, NextResponse } from 'next/server';
import { createServerSupabaseClient } from '@/lib/supabase/server';

export async function POST(request: NextRequest) {
  try {
    const { email } = await request.json();

    if (!email) {
      return NextResponse.json(
        { error: 'Email is required' },
        { status: 400 }
      );
    }

    const supabase = await createServerSupabaseClient();

    // Sign in with magic link - let Supabase handle user existence check
    const { error } = await supabase.auth.signInWithOtp({
      email,
      options: {
        shouldCreateUser: false,
        emailRedirectTo: `${process.env.NEXT_PUBLIC_SITE_URL}/auth/callback`,
      }
    });

    if (error) {
      console.error('Signin error:', error);
      console.error('Error details:', JSON.stringify(error, null, 2));

      // Handle specific error cases for user not found
      if (error.message.includes('User not found') ||
          error.message.includes('Invalid login credentials') ||
          error.message.includes('Unable to validate email address') ||
          error.message.includes('Email not confirmed') ||
          error.message.includes('signup_disabled') ||
          error.message.includes('Signups not allowed for otp') ||
          error.code === 'user_not_found') {
        return NextResponse.json(
          {
            error: 'Account not found. Please create an account first or check your email address.',
            userNotFound: true
          },
          { status: 404 }
        );
      }

      return NextResponse.json(
        { error: error.message },
        { status: 400 }
      );
    }

    return NextResponse.json({
      message: 'Magic link sent! Check your email to sign in.',
      success: true
    });

  } catch (error) {
    console.error('Signin API error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}