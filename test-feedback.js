// Test script to verify feedback email functionality
const fetch = require('node-fetch');

async function testFeedbackEmail() {
  try {
    const response = await fetch('http://localhost:3001/api/feedback/send', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        name: 'Test User',
        email: '<EMAIL>',
        subject: 'Test Feedback',
        message: 'This is a test feedback message to verify the email functionality.',
        rating: 5
      })
    });

    const data = await response.json();
    console.log('Response:', data);
    
    if (data.success) {
      console.log('✅ Feedback email sent successfully!');
    } else {
      console.log('❌ Failed to send feedback email:', data.error);
    }
  } catch (error) {
    console.error('❌ Error testing feedback email:', error);
  }
}

testFeedbackEmail();
