import { NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'

// GET /api/sync/users - Get user sync statistics
export async function GET() {
  try {
    console.log('📊 [MAIN-APP] Getting user sync statistics');

    // Count total users in main app
    const totalUsers = await prisma.profile.count();

    // Count CMS users in main app (users with cms_user_id)
    const cmsUsers = await prisma.profile.count({
      where: {
        cms_user_id: {
          not: null
        }
      }
    });

    // Count regular users (non-CMS)
    const regularUsers = totalUsers - cmsUsers;

    return NextResponse.json({
      success: true,
      stats: {
        totalUsers,
        cmsUsers,
        regularUsers,
        availableToImport: regularUsers // Users that could be imported to CMS
      }
    });

  } catch (error) {
    console.error('❌ [MAIN-APP] Error getting user sync stats:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to get user sync statistics'
      },
      { status: 500 }
    );
  }
}

export async function POST() {
  try {
    // Placeholder for user sync functionality
    // This would sync CMS users to main app database

    return NextResponse.json({
      success: true,
      syncedCount: 0,
      message: 'User sync not implemented yet'
    })
  } catch (error) {
    console.error('Error syncing users:', error)
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to sync users'
      },
      { status: 500 }
    )
  }
}
