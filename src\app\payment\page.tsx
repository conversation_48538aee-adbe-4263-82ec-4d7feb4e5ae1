"use client";

import React, { useEffect, useState, Suspense } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { toast } from "sonner";
import { useAuth } from "@/hooks/useAuth";
import { useProfile } from "@/hooks/useProfile";
import { getSubscriptionPlans } from "@/components/subscription/planData";
import { useSubscriptionStatus } from "@/hooks/useSubscriptionStatus";
import StripePaymentForm from "@/components/checkout/StripePaymentForm";
import { ArrowLeft } from "lucide-react";

const PaymentContent = () => {
    const router = useRouter();
    const { hasActiveSubscription, isLoading: subscriptionLoading } = useSubscriptionStatus();
    const searchParams = useSearchParams();
    const { user } = useAuth();
    const { profile } = useProfile();
    const [planId, setPlanId] = useState<string | null>(null);
    const [isUpgrade, setIsUpgrade] = useState(false);

    useEffect(() => {
        // Don't redirect while still loading subscription status
        if (subscriptionLoading) return;
        
        const upgradeParam = searchParams.get('upgrade') === '1';
        setIsUpgrade(upgradeParam);
        
        if (hasActiveSubscription && !upgradeParam) {
            toast.error("You already have an active subscription");
            router.push("/dashboard");
        }
    }, [hasActiveSubscription, router, searchParams, subscriptionLoading]);

    useEffect(() => {
        const planParam = searchParams.get('plan');
        if (planParam) {
            setPlanId(planParam);
        } else {
            toast.error("No plan selected");
            router.push("/pricing");
        }
    }, [searchParams, router]);

    const handlePaymentSuccess = () => {
        toast.success("Subscription activated! Welcome to Little Spark!");
        setTimeout(() => {
            router.push("/dashboard");
        }, 1500);
    };

    const handlePaymentError = (error: string) => {
        toast.error(`Payment failed: ${error}`);
    };

    const handleBackToPricing = () => {
        router.push("/pricing");
    };

    if (subscriptionLoading) {
        return (
            <div className="min-h-screen bg-gray-50 flex items-center justify-center">
                <div className="text-center">
                    <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
                    <p className="text-gray-600">Loading...</p>
                </div>
            </div>
        );
    }

    if (!planId) {
        return (
            <div className="min-h-screen bg-gray-50 flex items-center justify-center">
                <div className="text-center">
                    <p className="text-gray-600">No plan selected</p>
                </div>
            </div>
        );
    }

    const plans = getSubscriptionPlans();
    const planDetails = plans.find(plan => plan.planId === planId);

    return (
        <div className="min-h-screen bg-gray-50 py-8">
            <div className="max-w-2xl mx-auto px-4">
                {/* Header */}
                <div className="mb-8">
                    <button
                        onClick={handleBackToPricing}
                        className="flex items-center gap-2 text-gray-600 hover:text-gray-800 mb-4"
                    >
                        <ArrowLeft className="w-4 h-4" />
                        Back to pricing
                    </button>
                    <h1 className="text-3xl font-bold text-gray-900">
                        {isUpgrade ? 'Upgrade Your Plan' : 'Complete Your Subscription'}
                    </h1>
                    <p className="text-gray-600 mt-2">
                        {isUpgrade 
                            ? 'Upgrade to unlock more features' 
                            : 'Enter your payment details to get started with Little Spark'
                        }
                    </p>
                </div>

                {/* Plan Summary */}
                <div className="bg-white rounded-lg shadow-sm border p-6 mb-6">
                    <h3 className="font-semibold mb-4">Selected Plan</h3>
                    {planDetails && (
                        <div className="flex justify-between items-center p-4 bg-gray-50 rounded-lg">
                            <div>
                                <h4 className="font-medium">{planDetails.name}</h4>
                                <p className="text-sm text-gray-600">{planDetails.description}</p>
                            </div>
                            <div className="text-right">
                                <p className="text-2xl font-bold">{planDetails.price}</p>
                                <p className="text-sm text-gray-600">{planDetails.priceSubtext}</p>
                            </div>
                        </div>
                    )}
                </div>

                {/* Payment Form */}
                <div className="bg-white rounded-lg shadow-sm border p-6">
                    <h3 className="font-semibold mb-6">Payment Details</h3>
                    <StripePaymentForm
                        planId={planId}
                        onSuccess={handlePaymentSuccess}
                        onError={handlePaymentError}
                        userEmail={user?.email || ""}
                        userName={profile?.full_name || ""}
                    />
                </div>

                {/* Security Notice */}
                <div className="mt-6 text-center text-sm text-gray-500">
                    <p>🔒 Your payment information is secure and encrypted</p>
                    <p>Cancel anytime from your account settings</p>
                </div>
            </div>
        </div>
    );
};

export default function PaymentPage() {
    return (
        <Suspense fallback={
            <div className="min-h-screen bg-gray-50 flex items-center justify-center">
                <div className="text-center">
                    <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
                    <p className="text-gray-600">Loading payment form...</p>
                </div>
            </div>
        }>
            <PaymentContent />
        </Suspense>
    );
}
