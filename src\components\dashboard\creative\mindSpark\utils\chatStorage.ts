import { ChatMessage, convertTo<PERSON>son } from './types';
// import { supabase } from '@/lib/supabase/client';

// Save chat history to user's content
export const saveChatHistory = async (userId: string, updatedMessages: ChatMessage[]): Promise<boolean> => {
  try {
    // Database operations disabled to prevent permission errors
    // Use localStorage for chat history storage
    console.log('Database storage disabled - using localStorage only for MindSpark chat history');

    // Save to localStorage as primary storage
    const storageKey = `mindSparkChatHistory_${userId}`;
    const safeMessages = convertToJson(updatedMessages);
    localStorage.setItem(storageKey, JSON.stringify(safeMessages));

    return true;

    /* Commented out database operations
    // Convert ChatMessage[] to a format compatible with Supabase JSON storage
    const safeMessages = convertToJson(updatedMessages);

    const { data, error } = await supabase
      .from('user_content')
      .select('id')
      .eq('user_id', userId)
      .eq('type', 'chat')
      .single();

    if (error && error.code !== 'PGRST116') {
      console.error('Error checking for existing chat history:', error);
      return false;
    }

    // Update or insert logic
    if (data) {
      // Update existing record
      const { error: updateError } = await supabase
        .from('user_content')
        .update({ content_metadata: { messages: safeMessages } })
        .eq('id', data.id);

      if (updateError) {
        console.error('Error updating chat history:', updateError);
        return false;
      }
    } else {
      // Insert new record
      const { error: insertError } = await supabase
        .from('user_content')
        .insert({
          user_id: userId,
          type: 'chat',
          title: 'Chat History',
          content_metadata: { messages: safeMessages }
        });

      if (insertError) {
        console.error('Error inserting chat history:', insertError);
        return false;
      }
    }

    return true;
    */
  } catch (error) {
    console.error('Error saving chat history:', error);
    return false;
  }
};

// Load chat history from localStorage
export const loadChatHistory = async (userId: string): Promise<ChatMessage[]> => {
  try {
    // Load from localStorage
    const storageKey = `mindSparkChatHistory_${userId}`;
    const stored = localStorage.getItem(storageKey);

    if (stored) {
      const messages = JSON.parse(stored) as ChatMessage[];
      console.log('Loaded MindSpark chat history from localStorage:', messages.length, 'messages');
      return messages;
    }

    console.log('No MindSpark chat history found in localStorage');
    return [];
  } catch (error) {
    console.error('Error loading MindSpark chat history:', error);
    return [];
  }
};

// Clear chat history
export const clearChatHistory = async (userId: string): Promise<void> => {
  try {
    const storageKey = `mindSparkChatHistory_${userId}`;
    localStorage.removeItem(storageKey);
    console.log('MindSpark chat history cleared from localStorage');
  } catch (error) {
    console.error('Error clearing MindSpark chat history:', error);
  }
};
