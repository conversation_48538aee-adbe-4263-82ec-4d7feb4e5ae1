"use client";

import { useState } from "react";
import { authService } from "@/lib/auth/auth-service";
import { fredoka } from "@/lib/fonts";

type AuthMode = "signin" | "signup";

export default function Auth() {
    const [mode, setMode] = useState<AuthMode>("signin");
    const [formData, setFormData] = useState({
        fullName: "",
        email: "",
    });
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState<string | null>(null);
    const [success, setSuccess] = useState<string | null>(null);

    const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        setFormData({
            ...formData,
            [e.target.name]: e.target.value,
        });
    };

    const handleMagicLink = async (e: React.FormEvent) => {
        e.preventDefault();
        setError(null);
        setSuccess(null);
        setLoading(true);

        try {
            const { error, userNotFound } = await authService.signInWithMagicLink(
                formData.email
            );

            if (error) {
                if (userNotFound) {
                    setError(`${error.message} Would you like to create an account instead?`);
                    // Auto-switch to signup mode after a short delay
                    setTimeout(() => {
                        setMode("signup");
                        setError(null);
                    }, 3000);
                } else {
                    setError(error.message);
                }
            } else {
                setSuccess("Check your email for a magic link to sign in!");
            }
        } catch {
            setError("An unexpected error occurred");
        } finally {
            setLoading(false);
        }
    };

    const handleSignUp = async (e: React.FormEvent) => {
        e.preventDefault();
        setError(null);
        setSuccess(null);
        setLoading(true);

        try {
            const { error } = await authService.signUpWithMagicLink({
                email: formData.email,
                fullName: formData.fullName,
            });

            if (error) {
                setError(error.message);
            } else {
                setSuccess(
                    "Account created! Check your email to verify your account."
                );
            }
        } catch {
            setError("An unexpected error occurred");
        } finally {
            setLoading(false);
        }
    };

    return (
        <div className="min-h-screen bg-gray-50 flex flex-col justify-center py-12 px-4 sm:px-6 lg:px-8">
            <div className="mx-auto w-full max-w-md px-4 sm:px-0">
                <div className="bg-white py-8 px-6 shadow-lg rounded-2xl border border-gray-100 sm:px-8">
                    {/* Header */}
                    <div className={`${fredoka.className} text-center mb-8`}>
                        <h1 className="text-2xl font-bold text-gray-900 mb-3">
                            Sign In to Little Spark
                        </h1>
                        <p className="text-gray-500 text-sm">
                            Sign in or create an account to continue
                        </p>
                    </div>
                    {/* Tab Navigation */}
                    <div className="flex bg-gray-100 rounded-full p-1 mb-6">
                        <button
                            onClick={() => setMode("signin")}
                            className={`flex-1 py-2.5 px-4 text-sm font-medium rounded-full transition-all duration-200 ${
                                mode === "signin"
                                    ? "bg-white text-gray-900 shadow-sm"
                                    : "text-gray-600 hover:text-gray-800"
                            }`}
                        >
                            Sign In
                        </button>
                        <button
                            onClick={() => setMode("signup")}
                            className={`flex-1 py-2.5 px-4 text-sm font-medium rounded-full transition-all duration-200 ${
                                mode === "signup"
                                    ? "bg-white text-gray-900 shadow-sm"
                                    : "text-gray-600 hover:text-gray-800"
                            }`}
                        >
                            Sign Up
                        </button>
                    </div>

                    {/* Messages */}
                    {success && (
                        <div className="mb-6 p-4 text-sm text-green-800 bg-green-50 border border-green-200 rounded-full">
                            <div className="flex items-center">
                                <svg
                                    className="w-5 h-5 text-green-500 mr-2"
                                    fill="currentColor"
                                    viewBox="0 0 20 20"
                                >
                                    <path
                                        fillRule="evenodd"
                                        d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                                        clipRule="evenodd"
                                    />
                                </svg>
                                {success}
                            </div>
                        </div>
                    )}
                    {error && (
                        <div className="mb-6 p-4 text-sm text-red-800 bg-red-50 border border-red-200 rounded-full">
                            <div className="flex items-center">
                                <svg
                                    className="w-5 h-5 text-red-500 mr-2"
                                    fill="currentColor"
                                    viewBox="0 0 20 20"
                                >
                                    <path
                                        fillRule="evenodd"
                                        d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z"
                                        clipRule="evenodd"
                                    />
                                </svg>
                                {error}
                            </div>
                        </div>
                    )}

                    {/* Sign In Form */}
                    {mode === "signin" && (
                        <form onSubmit={handleMagicLink} className="space-y-5">
                            <div>
                                <label
                                    htmlFor="email"
                                    className="block text-sm font-medium text-gray-700 mb-2"
                                >
                                    Email address
                                </label>
                                <input
                                    id="email"
                                    name="email"
                                    type="email"
                                    required
                                    value={formData.email}
                                    onChange={handleInputChange}
                                    className="appearance-none block w-full px-3 py-3 border border-gray-300 rounded-full placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-littlespark-primary focus:border-littlespark-primary transition-all duration-200"
                                    placeholder="Enter your email"
                                />
                            </div>

                            <button
                                type="submit"
                                disabled={loading}
                                className="w-full flex justify-center py-3 px-4 border border-transparent rounded-full text-sm font-medium text-white bg-littlespark-primary hover:bg-littlespark-primary-hover focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-littlespark-primary disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200"
                            >
                                {loading ? (
                                    <div className="flex items-center">
                                        <svg
                                            className="animate-spin -ml-1 mr-3 h-4 w-4 text-white"
                                            xmlns="http://www.w3.org/2000/svg"
                                            fill="none"
                                            viewBox="0 0 24 24"
                                        >
                                            <circle
                                                className="opacity-25"
                                                cx="12"
                                                cy="12"
                                                r="10"
                                                stroke="currentColor"
                                                strokeWidth="4"
                                            ></circle>
                                            <path
                                                className="opacity-75"
                                                fill="currentColor"
                                                d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                                            ></path>
                                        </svg>
                                        Sending...
                                    </div>
                                ) : (
                                    "Email me a login link"
                                )}
                            </button>

                            <div className="text-center pt-1">
                                <p className="text-xs text-gray-500 mb-1">
                                    You&apos;ll receive a magic link to sign in
                                    securely
                                </p>
                                <p className="text-xs text-gray-600 font-medium">
                                    No password needed!
                                </p>
                            </div>
                        </form>
                    )}

                    {/* Sign Up Form */}
                    {mode === "signup" && (
                        <form onSubmit={handleSignUp} className="space-y-5">
                            <div>
                                <label
                                    htmlFor="fullName"
                                    className="block text-sm font-medium text-gray-700 mb-2"
                                >
                                    Full Name
                                </label>
                                <input
                                    id="fullName"
                                    name="fullName"
                                    type="text"
                                    required
                                    value={formData.fullName}
                                    onChange={handleInputChange}
                                    className="appearance-none block w-full px-3 py-3 border border-gray-300 rounded-full placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-littlespark-primary focus:border-littlespark-primary transition-all duration-200"
                                    placeholder="John Doe"
                                />
                            </div>

                            <div>
                                <label
                                    htmlFor="email-signup"
                                    className="block text-sm font-medium text-gray-700 mb-2"
                                >
                                    Email
                                </label>
                                <input
                                    id="email-signup"
                                    name="email"
                                    type="email"
                                    required
                                    value={formData.email}
                                    onChange={handleInputChange}
                                    className="appearance-none block w-full px-3 py-3 border border-gray-300 rounded-full placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-littlespark-primary focus:border-littlespark-primary transition-all duration-200"
                                    placeholder="<EMAIL>"
                                />
                            </div>

                            <button
                                type="submit"
                                disabled={loading}
                                className="w-full flex justify-center py-3 px-4 border border-transparent rounded-full text-sm font-medium text-white bg-littlespark-primary hover:bg-littlespark-primary-hover focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-littlespark-primary disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200"
                            >
                                {loading ? (
                                    <div className="flex items-center">
                                        <svg
                                            className="animate-spin -ml-1 mr-3 h-4 w-4 text-white"
                                            xmlns="http://www.w3.org/2000/svg"
                                            fill="none"
                                            viewBox="0 0 24 24"
                                        >
                                            <circle
                                                className="opacity-25"
                                                cx="12"
                                                cy="12"
                                                r="10"
                                                stroke="currentColor"
                                                strokeWidth="4"
                                            ></circle>
                                            <path
                                                className="opacity-75"
                                                fill="currentColor"
                                                d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                                            ></path>
                                        </svg>
                                        Creating Account...
                                    </div>
                                ) : (
                                    "Create Account"
                                )}
                            </button>
                        </form>
                    )}
                </div>
            </div>
        </div>
    );
}
