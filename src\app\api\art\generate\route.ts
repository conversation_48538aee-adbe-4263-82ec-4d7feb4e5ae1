import { NextResponse } from 'next/server';
import { moderateContent } from '@/utils/ai/contentModeration';

const REPLICATE_API_TOKEN = process.env.REPLICATE_API_TOKEN;
const MODEL_VERSIONS: Record<string, string> = {
  // Stability AI Stable Diffusion 3.5 Large (general purpose, good for various artistic styles)
  // base: 'stability-ai/stable-diffusion-3.5-large',
  // cartoon: 'stability-ai/stable-diffusion-3.5-large',
  // comic: 'stability-ai/stable-diffusion-3.5-large',
  // watercolor: 'stability-ai/stable-diffusion-3.5-large',

  // Google Imagen 4 (excels in photorealism, cinematic quality)
  realistic: 'google/imagen-4',

  // DeepSeek Janus-Pro-7B (multimodal, good for simpler or pixelated styles given its 384x384 output)
  pixel: 'deepseek-ai/janus-pro-7b',
};

export async function POST(request: Request) {
  try {
    const { prompt, style = 'realistic', aspectRatio = '1:1' } = await request.json();

    const userKey = request.headers.get('x-forwarded-for') ?? 'anonymous';
    const inCheck = await moderateContent(prompt || '', 'prompt', userKey);
    if (!inCheck.isAppropriate) {
      return NextResponse.json({ success: false, error: inCheck.reason, blocked: inCheck.blocked ?? false }, { status: 400 });
    }

    const styleKey = String(style).toLowerCase();

    if (!prompt) {
      return NextResponse.json(
        { success: false, error: 'Prompt is required' },
        { status: 400 }
      );
    }

    if (!REPLICATE_API_TOKEN) {
      return NextResponse.json(
        { success: false, error: 'Replicate API token not configured' },
        { status: 500 }
      );
    }

    // Dimensions
    let width = 768,
      height = 768;
    switch (aspectRatio) {
      case '16:9':
        width = 1024;
        height = 576;
        break;
      case '9:16':
        width = 576;
        height = 1024;
        break;
      case '4:3':
        width = 1024;
        height = 768;
        break;
      case '3:4':
        width = 768;
        height = 1024;
        break;
    }

    // Style modifier
    let styledPrompt = prompt;
    let negativePrompt = ''; // Initialize negativePrompt for each case
    const modelVersion = MODEL_VERSIONS[styleKey] || MODEL_VERSIONS.base;

    // Define base negative prompt for all models
    const baseNegativePrompt = 'low quality, blurry, distorted, deformed, bad anatomy, ugly, tiling, duplicate, cropped, out of frame, extra limbs, disfigured, gross proportions, malformed limbs, missing arms, missing legs, extra arms, extra legs, mutated hands, fused fingers, too many fingers, long neck, too many heads, bad lighting, underexposed, overexposed, grayscale, black and white, amateur, poor composition, boring, dull, unsettling, gore, explicit, violent';

    // Set specific negative prompts and style modifiers based on model and style
    if (modelVersion === 'google/imagen-4') {
      negativePrompt = 'cartoon, illustration, painting, anime, sketch, drawing, graphic, low poly, pixel art, blurry, distorted, unnatural colors';
      switch (styleKey) {
        case 'realistic':
          styledPrompt += ', ultra-realistic photograph, cinematic, intricately detailed, lifelike, natural lighting, shallow depth of field, sharp focus, volumetric light, hyperrealistic, professional photo';
          break;
        default:
          styledPrompt += ', cinematic, photorealistic, high detail';
          break;
      }
    } else if (modelVersion === 'deepseek-ai/janus-pro-7b') {
      negativePrompt = 'high resolution, smooth, photorealistic, painting, drawing, blurry, complex details, many colors, realistic shading';
      // Janus-Pro has a fixed output resolution
      width = 384;
      height = 384;
      switch (styleKey) {
        case 'pixel':
          styledPrompt += ', retro pixel art style, 8-bit, 16-bit, game sprite, chunky pixels, low resolution aesthetic, vibrant block colors, nostalgic';
          break;
        default:
          styledPrompt += ', simple, stylized, blocky, basic shapes';
          break;
      }
    } else { // Default to stability-ai/stable-diffusion-3.5-large
      negativePrompt = 'photorealistic, realistic, detailed textures, shadow, complex shading'; // Default negative for artistic
      switch (styleKey) {
        case 'cartoon':
          styledPrompt += ', vibrant animated cartoon style, bold outlines, smooth colors, whimsical, cute, simple shapes, 2D animation feel';
          negativePrompt += ', photorealistic, realistic, detailed textures, shadow, complex shading';
          break;
        case 'comic':
          styledPrompt += ', graphic novel comic book style, strong linework, halftone dots, vibrant flat colors, dynamic action, narrative panel, expressive characters';
          negativePrompt += ', photorealistic, painting, blurry, soft, gentle';
          break;
        case 'watercolor':
          styledPrompt += ', delicate watercolor painting, soft ethereal tones, translucent washes, visible brushstrokes, blooming effect, serene, peaceful, hand-painted texture';
          negativePrompt += ', sharp edges, solid colors, digital art, photorealistic, harsh lighting, drawing';
          break;
        default: // base style
          styledPrompt += ', vibrant, imaginative, child-friendly artistic style';
          break;
      }
    }
    // Combine base negative prompt with style-specific negative prompt
    negativePrompt = `${baseNegativePrompt}, ${negativePrompt}`;

    // Add child-friendly safety modifiers for all prompts
    styledPrompt += ', safe for children, positive, no violence, no scary content, educational, inspiring, bright colors, happy mood, fantasy elements';

    // Create prediction
    const createRes = await fetch('https://api.replicate.com/v1/predictions', {
      method: 'POST',
      headers: {
        Authorization: `Token ${REPLICATE_API_TOKEN}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        version: modelVersion, // Use selected model version
        input: {
          prompt: styledPrompt,
          negative_prompt: negativePrompt,
          width,
          height,
          num_outputs: 1,
          scheduler: 'K_EULER',
          num_inference_steps: 50,
          guidance_scale: 7.5
        }
      })
    });

    if (!createRes.ok) {
      const err = await createRes.text();
      throw new Error(`Replicate error: ${err}`);
    }

    const prediction = await createRes.json();
    const { id } = prediction;

    // Poll until finished
    let status = prediction.status;
    let outputUrl: string | undefined;
    let attempts = 0;
    while (status !== 'succeeded' && status !== 'failed' && attempts < 40) {
      await new Promise((r) => setTimeout(r, 4000));
      const poll = await fetch(`https://api.replicate.com/v1/predictions/${id}`, {
        headers: { Authorization: `Token ${REPLICATE_API_TOKEN}` }
      });
      const pollJson = await poll.json();
      status = pollJson.status;
      if (status === 'succeeded') {
        outputUrl = pollJson.output?.[0];
      }
      attempts++;
    }

    if (status !== 'succeeded' || !outputUrl) {
      throw new Error('Image generation failed or timed out');
    }

    return NextResponse.json({
      success: true,
      imageUrl: outputUrl,
      metadata: { styledPrompt, prompt, style: styleKey, aspectRatio, width, height }
    });
  } catch (e) {
    console.error('[api/art/generate] error', e);
    return NextResponse.json(
      { success: false, error: e instanceof Error ? e.message : 'failed' },
      { status: 500 }
    );
  }
} 