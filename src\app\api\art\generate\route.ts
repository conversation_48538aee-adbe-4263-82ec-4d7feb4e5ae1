import { NextResponse } from 'next/server';
import { moderateContent } from '@/utils/ai/contentModeration';

const REPLICATE_API_TOKEN = process.env.REPLICATE_API_TOKEN;
const MODEL_VERSIONS: Record<string, string> = {
  // Using Flux Schnell (free) for testing, then we can upgrade to Pro
  base: 'black-forest-labs/flux-schnell',
  cartoon: 'black-forest-labs/flux-schnell',
  comic: 'black-forest-labs/flux-schnell',
  watercolor: 'black-forest-labs/flux-schnell',
  realistic: 'black-forest-labs/flux-schnell',
  pixel: 'black-forest-labs/flux-schnell',
};

export async function POST(request: Request) {
  try {
    const { prompt, style = 'realistic', aspectRatio = '1:1' } = await request.json();

    const userKey = request.headers.get('x-forwarded-for') ?? 'anonymous';
    const inCheck = await moderateContent(prompt || '', 'prompt', userKey);
    if (!inCheck.isAppropriate) {
      return NextResponse.json({ success: false, error: inCheck.reason, blocked: inCheck.blocked ?? false }, { status: 400 });
    }

    const styleKey = String(style).toLowerCase();

    if (!prompt) {
      return NextResponse.json(
        { success: false, error: 'Prompt is required' },
        { status: 400 }
      );
    }

    if (!REPLICATE_API_TOKEN) {
      return NextResponse.json(
        { success: false, error: 'Replicate API token not configured' },
        { status: 500 }
      );
    }

    // Dimensions
    let width = 768,
      height = 768;
    switch (aspectRatio) {
      case '16:9':
        width = 1024;
        height = 576;
        break;
      case '9:16':
        width = 576;
        height = 1024;
        break;
      case '4:3':
        width = 1024;
        height = 768;
        break;
      case '3:4':
        width = 768;
        height = 1024;
        break;
    }

    // Style modifier
    let styledPrompt = prompt;
    const modelVersion = MODEL_VERSIONS[styleKey] || MODEL_VERSIONS.base;

    // Set style modifiers for Flux model with strong style enforcement
    switch (styleKey) {
      case 'realistic':
        // Strong realistic modifiers to override cartoon tendencies
        styledPrompt = `photorealistic, real life, actual photograph, ${styledPrompt}, ultra-realistic, cinematic photography, professional DSLR camera, natural lighting, sharp focus, high detail, lifelike textures, realistic skin, realistic fur, realistic materials, not cartoon, not animated, not illustration, photographic quality, 8k resolution, hyperrealistic`;
        break;
      case 'cartoon':
        styledPrompt += ', vibrant animated cartoon style, bold outlines, smooth colors, whimsical, cute, simple shapes, 2D animation feel, Disney-style, colorful, illustrated, animated';
        break;
      case 'comic':
        styledPrompt += ', graphic novel comic book style, strong linework, halftone dots, vibrant flat colors, dynamic action, narrative panel, expressive characters, Marvel style, illustrated';
        break;
      case 'watercolor':
        styledPrompt += ', delicate watercolor painting, soft ethereal tones, translucent washes, visible brushstrokes, blooming effect, serene, peaceful, hand-painted texture, artistic, painted';
        break;
      case 'pixel':
        styledPrompt += ', retro pixel art style, 8-bit, 16-bit, game sprite, chunky pixels, low resolution aesthetic, vibrant block colors, nostalgic, video game art, pixelated';
        break;
      default: // base style
        styledPrompt += ', vibrant, imaginative, child-friendly artistic style, colorful, beautiful, high quality, illustrated';
        break;
    }
    // negativePrompt is already set above with style-specific content

    // Add child-friendly safety modifiers for all prompts
    styledPrompt += ', safe for children, positive, no violence, no scary content, educational, inspiring, bright colors, happy mood, fantasy elements';

    // Create prediction
    const createRes = await fetch('https://api.replicate.com/v1/predictions', {
      method: 'POST',
      headers: {
        Authorization: `Token ${REPLICATE_API_TOKEN}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        version: modelVersion, // Use Flux model
        input: {
          prompt: styledPrompt,
          width,
          height,
          num_outputs: 1,
          num_inference_steps: 4, // Flux Schnell uses 4 steps
          seed: Math.floor(Math.random() * 1000000),
          output_format: "webp",
          output_quality: 90
        }
      })
    });

    if (!createRes.ok) {
      const err = await createRes.text();
      console.error('❌ [ART-GENERATE] Replicate API error:', {
        status: createRes.status,
        statusText: createRes.statusText,
        error: err,
        modelVersion,
        requestBody: { prompt: styledPrompt, width, height }
      });
      throw new Error(`Replicate error (${createRes.status}): ${err}`);
    }

    const prediction = await createRes.json();
    const { id } = prediction;

    // Poll until finished
    let status = prediction.status;
    let outputUrl: string | undefined;
    let attempts = 0;
    console.log('🔄 [ART-GENERATE] Starting polling for prediction:', id);

    while (status !== 'succeeded' && status !== 'failed' && attempts < 40) {
      await new Promise((r) => setTimeout(r, 4000));
      const poll = await fetch(`https://api.replicate.com/v1/predictions/${id}`, {
        headers: { Authorization: `Token ${REPLICATE_API_TOKEN}` }
      });
      const pollJson = await poll.json();
      status = pollJson.status;
      console.log(`🔄 [ART-GENERATE] Poll ${attempts + 1}: status=${status}`);

      if (status === 'succeeded') {
        outputUrl = pollJson.output?.[0];
        console.log('✅ [ART-GENERATE] Output received:', outputUrl);
      } else if (status === 'failed') {
        console.error('❌ [ART-GENERATE] Generation failed:', pollJson.error);
      }
      attempts++;
    }

    if (status !== 'succeeded' || !outputUrl) {
      console.error('❌ [ART-GENERATE] Generation failed:', { status, outputUrl, attempts });
      throw new Error('Image generation failed or timed out');
    }

    console.log('✅ [ART-GENERATE] Success:', { outputUrl, status, attempts });

    // Validate the output URL
    if (!outputUrl.startsWith('http')) {
      console.error('❌ [ART-GENERATE] Invalid URL format:', outputUrl);
      throw new Error('Invalid image URL format received');
    }

    return NextResponse.json({
      success: true,
      imageUrl: outputUrl,
      metadata: { styledPrompt, prompt, style: styleKey, aspectRatio, width, height }
    });
  } catch (e) {
    console.error('[api/art/generate] error', e);
    return NextResponse.json(
      { success: false, error: e instanceof Error ? e.message : 'failed' },
      { status: 500 }
    );
  }
} 