import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { createServerSupabaseClient } from '@/lib/supabase/server';

// GET /api/mind-spark/history - Get user's question history and stats
export async function GET(request: NextRequest) {
  try {
    console.log('🔍 [HISTORY] Starting history fetch...');

    // Get authenticated user
    const supabase = await createServerSupabaseClient();
    const { data: { user } } = await supabase.auth.getUser();

    if (!user) {
      console.log('❌ [HISTORY] No authenticated user');
      return NextResponse.json(
        {
          success: false,
          error: 'Authentication required'
        },
        { status: 401 }
      );
    }

    console.log('✅ [HISTORY] User authenticated:', user.id);

    const { searchParams } = new URL(request.url);
    const limit = parseInt(searchParams.get('limit') || '20');
    const age_group = searchParams.get('age_group');

    // Build where clause for answers
    const whereClause: {
      user_id: string;
      question?: {
        age_group: string;
      };
    } = {
      user_id: user.id
    };

    if (age_group) {
      whereClause.question = {
        age_group: age_group
      };
    }

    console.log('📊 [HISTORY] Fetching answers for user:', user.id, 'with limit:', limit);

    // Get user's answers with question details
    const answers = await prisma.mindSparkAnswer.findMany({
      where: whereClause,
      include: {
        question: {
          select: {
            id: true,
            question: true,
            age_group: true,
            category: true,
            difficulty: true,
            is_ai_generated: true
          }
        }
      },
      orderBy: {
        answered_at: 'desc'
      },
      take: limit
    });

    console.log('✅ [HISTORY] Found', answers.length, 'answers');

    // Get total count of answers
    const totalAnswered = await prisma.mindSparkAnswer.count({
      where: {
        user_id: user.id
      }
    });

    console.log('📊 [HISTORY] Skipping category breakdown for now...');

    // Get recent activity (last 7 days)
    const sevenDaysAgo = new Date();
    sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);

    const recentActivity = await prisma.mindSparkAnswer.count({
      where: {
        user_id: user.id,
        answered_at: {
          gte: sevenDaysAgo
        }
      }
    });

    // For now, return empty category stats since we simplified the query
    // TODO: Implement proper category breakdown later
    const transformedCategoryStats: Array<{
      category: string;
      age_group: string;
      answer_count: number;
    }> = [];

    // Total answered is already calculated above

    // Get streak information (simplified for now)
    console.log('📈 [HISTORY] Calculating simple streak...');
    const streakData = { currentStreak: 0, longestStreak: 0 };

    return NextResponse.json({
      success: true,
      history: answers.map(answer => ({
        id: answer.id,
        answer: answer.answer,
        answered_at: answer.answered_at,
        question: answer.question
      })),
      stats: {
        total_answered: totalAnswered,
        recent_activity: recentActivity,
        category_breakdown: transformedCategoryStats,
        current_streak: streakData.currentStreak,
        longest_streak: streakData.longestStreak
      }
    });

  } catch (error) {
    console.error('❌ [MIND-SPARK-HISTORY] Error fetching history:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to fetch history',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}


