import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { createServerSupabaseClient } from '@/lib/supabase/server';

// GET /api/mind-spark/history - Get user's question history and stats
export async function GET(request: NextRequest) {
  try {
    // Get authenticated user
    const supabase = await createServerSupabaseClient();
    const { data: { user } } = await supabase.auth.getUser();
    
    if (!user) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Authentication required' 
        },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const limit = parseInt(searchParams.get('limit') || '20');
    const age_group = searchParams.get('age_group');

    // Build where clause for answers
    const whereClause: any = {
      user_id: user.id
    };

    if (age_group) {
      whereClause.question = {
        age_group: age_group
      };
    }

    // Get user's answers with question details
    const answers = await prisma.mindSparkAnswer.findMany({
      where: whereClause,
      include: {
        question: {
          select: {
            id: true,
            question: true,
            age_group: true,
            category: true,
            difficulty: true,
            is_ai_generated: true
          }
        }
      },
      orderBy: {
        answered_at: 'desc'
      },
      take: limit
    });

    // Get statistics
    const stats = await prisma.mindSparkAnswer.groupBy({
      by: ['question'],
      where: {
        user_id: user.id
      },
      _count: {
        id: true
      }
    });

    // Get category breakdown
    const categoryStats = await prisma.$queryRaw`
      SELECT 
        q.category,
        q.age_group,
        COUNT(a.id) as answer_count
      FROM mind_spark_answers a
      JOIN mind_spark_questions q ON a.question_id = q.id
      WHERE a.user_id = ${user.id}
      GROUP BY q.category, q.age_group
      ORDER BY answer_count DESC
    ` as Array<{
      category: string;
      age_group: string;
      answer_count: bigint;
    }>;

    // Get recent activity (last 7 days)
    const sevenDaysAgo = new Date();
    sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);

    const recentActivity = await prisma.mindSparkAnswer.count({
      where: {
        user_id: user.id,
        answered_at: {
          gte: sevenDaysAgo
        }
      }
    });

    // Transform category stats
    const transformedCategoryStats = categoryStats.map(stat => ({
      category: stat.category,
      age_group: stat.age_group,
      answer_count: Number(stat.answer_count)
    }));

    // Calculate total questions answered
    const totalAnswered = stats.length;

    // Get streak information (consecutive days with answers)
    const streakData = await calculateAnswerStreak(user.id);

    return NextResponse.json({
      success: true,
      history: answers.map(answer => ({
        id: answer.id,
        answer: answer.answer,
        answered_at: answer.answered_at,
        question: answer.question
      })),
      stats: {
        total_answered: totalAnswered,
        recent_activity: recentActivity,
        category_breakdown: transformedCategoryStats,
        current_streak: streakData.currentStreak,
        longest_streak: streakData.longestStreak
      }
    });

  } catch (error) {
    console.error('❌ [MIND-SPARK-HISTORY] Error fetching history:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to fetch history',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

// Helper function to calculate answer streak
async function calculateAnswerStreak(userId: string) {
  try {
    // Get all answer dates for the user, grouped by date
    const answerDates = await prisma.$queryRaw`
      SELECT 
        DATE(answered_at) as answer_date,
        COUNT(*) as answers_count
      FROM mind_spark_answers 
      WHERE user_id = ${userId}
      GROUP BY DATE(answered_at)
      ORDER BY answer_date DESC
    ` as Array<{
      answer_date: Date;
      answers_count: bigint;
    }>;

    if (answerDates.length === 0) {
      return { currentStreak: 0, longestStreak: 0 };
    }

    let currentStreak = 0;
    let longestStreak = 0;
    let tempStreak = 0;
    
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    
    // Check if user answered today or yesterday to start current streak
    const latestDate = new Date(answerDates[0].answer_date);
    latestDate.setHours(0, 0, 0, 0);
    
    const daysDiff = Math.floor((today.getTime() - latestDate.getTime()) / (1000 * 60 * 60 * 24));
    
    if (daysDiff <= 1) {
      // Start counting current streak
      let streakDate = new Date(latestDate);
      
      for (const dateEntry of answerDates) {
        const answerDate = new Date(dateEntry.answer_date);
        answerDate.setHours(0, 0, 0, 0);
        
        if (answerDate.getTime() === streakDate.getTime()) {
          currentStreak++;
          tempStreak++;
          streakDate.setDate(streakDate.getDate() - 1);
        } else {
          break;
        }
      }
    }

    // Calculate longest streak
    tempStreak = 0;
    let expectedDate = new Date(answerDates[0].answer_date);
    expectedDate.setHours(0, 0, 0, 0);

    for (const dateEntry of answerDates) {
      const answerDate = new Date(dateEntry.answer_date);
      answerDate.setHours(0, 0, 0, 0);
      
      if (answerDate.getTime() === expectedDate.getTime()) {
        tempStreak++;
        longestStreak = Math.max(longestStreak, tempStreak);
        expectedDate.setDate(expectedDate.getDate() - 1);
      } else {
        tempStreak = 1;
        expectedDate = new Date(answerDate);
        expectedDate.setDate(expectedDate.getDate() - 1);
      }
    }

    return { currentStreak, longestStreak };

  } catch (error) {
    console.error('Error calculating streak:', error);
    return { currentStreak: 0, longestStreak: 0 };
  }
}
