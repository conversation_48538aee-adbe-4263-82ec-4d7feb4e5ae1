import { NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { createServerSupabaseClient } from '@/lib/supabase/server';

export async function GET() {
  const supabase = await createServerSupabaseClient();
  const { data: { user } } = await supabase.auth.getUser();

  if (!user) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }

  try {
    const profile = await prisma.profile.findUnique({
      where: { id: user.id },
      select: {
        subscription_status: true,
        plan_name: true,
        plan_id: true,
        subscription_start: true,
        subscription_end: true,
        billing_cycle: true,
        trial_end: true,
        trial_start: true,
        trial_used: true,
      },
    });

    if (!profile) {
      // Create profile for new user with trial available
      try {
        // const newProfile = await prisma.profile.create({
        //   data: {
        //     id: user.id,
        //     email: user.email!,
        //     full_name: user.user_metadata?.full_name || null,
        //     trial_used: false,
        //   }
        // });

        console.log(`Created new profile for user ${user.id} in subscription status check`);

        // Return default new user status
        return NextResponse.json({
          subscription_status: null,
          plan_name: null,
          plan_id: null,
          subscription_start: null,
          subscription_end: null,
          billing_cycle: null,
          trial_end: null,
          trial_start: null,
          trial_used: false,
        });
      } catch (error) {
        console.error('Error creating profile in subscription status check:', error);
        return NextResponse.json({ error: 'Failed to create profile' }, { status: 500 });
      }
    }

    // Enhanced status logic: Check for trial expiration and payment records
    const enhancedStatus = { ...profile };
    let statusUpdated = false;

    // Check for trialing status that should be active
    if (profile.subscription_status === 'trialing') {
      // Check if there are successful payments regardless of trial expiration
      const successfulPayments = await prisma.payment.findMany({
        where: {
          profile_id: user.id,
          status: 'succeeded',
          created_at: {
            gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000) // Within last 30 days
          }
        },
        orderBy: { created_at: 'desc' },
        take: 1
      });

      if (successfulPayments.length > 0) {
        const latestPayment = successfulPayments[0];

        // If trial has ended OR if we have payments after trial start, update to active
        const trialEnd = profile.trial_end ? new Date(profile.trial_end) : null;
        const now = new Date();
        const trialExpired = trialEnd && now > trialEnd;
        const paymentAfterTrialStart = profile.trial_start ?
          latestPayment.created_at > new Date(profile.trial_start) : true;

        if (trialExpired || paymentAfterTrialStart) {
          console.log(`Updating user ${user.id} from trialing to active:`, {
            trialExpired,
            paymentAfterTrialStart,
            latestPaymentDate: latestPayment.created_at,
            trialEnd: trialEnd?.toISOString(),
            paymentAmount: latestPayment.amount
          });

          await prisma.profile.update({
            where: { id: user.id },
            data: {
              subscription_status: 'active',
              trial_used: true,
              updated_at: new Date()
            }
          });

          enhancedStatus.subscription_status = 'active';
          enhancedStatus.trial_used = true;
          statusUpdated = true;
        }
      }
    }

    // Check for incomplete status that should be active
    if (profile.subscription_status === 'incomplete' && !statusUpdated) {
      const successfulPayments = await prisma.payment.findMany({
        where: {
          profile_id: user.id,
          status: 'succeeded',
          created_at: {
            gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000) // Within last 30 days
          }
        },
        orderBy: { created_at: 'desc' },
        take: 1
      });

      if (successfulPayments.length > 0) {
        console.log(`Updating user ${user.id} from incomplete to active due to successful payments`);

        await prisma.profile.update({
          where: { id: user.id },
          data: {
            subscription_status: 'active',
            updated_at: new Date()
          }
        });

        enhancedStatus.subscription_status = 'active';
        statusUpdated = true;
      }
    }

    return NextResponse.json(enhancedStatus);
  } catch (error) {
    console.error('Error fetching subscription status:', error);
    return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 });
  }
}
