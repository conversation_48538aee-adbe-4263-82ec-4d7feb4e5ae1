
export interface Question {
  id: string;
  question: string;
  age_group: string;
  difficulty: 'easy' | 'medium' | 'hard';
  category: string;
  is_ai_generated: boolean;
  created_at: string;
  is_answered?: boolean;
  user_answer?: {
    id: string;
    answer: string;
    answered_at: string;
  } | null;
}

export type AgeGroup = '5-7' | '8-10' | '11-13' | '14-16' | '17+';

export interface MindSparkProps {
  userAge?: number;
  safeMode?: boolean;
  toolType?: 'general' | 'stem' | 'language' | 'story' | 'art' | 'game' | 'music';
}
