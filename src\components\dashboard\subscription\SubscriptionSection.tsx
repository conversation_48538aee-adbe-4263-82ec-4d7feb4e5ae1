"use client";
import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { useSubscription } from '@/hooks/subscription';
import Link from 'next/link';
import { useAuth } from '@/hooks/useAuth';
import { toast } from 'sonner';
// import { useRouter } from 'next/navigation';
import { getSubscriptionPlans } from '@/components/subscription/planData';
import { RefreshCw } from 'lucide-react';
import { ConfirmationDialog } from '@/components/ui/confirmation-dialog';

export function SubscriptionSection() {
  const { user } = useAuth();
  const {
    subscriptionStatus,
    hasActiveSubscription,
    refreshSubscriptionStatus,
    isLoading,
    accessResult,
    isTrialExpired,
    daysUntilExpiry
  } = useSubscription();
  const plansData = getSubscriptionPlans();
  const currentPlanData = subscriptionStatus?.plan_id ? plansData.find(p => p.planId === subscriptionStatus.plan_id) : null;
  const billingCycleText = currentPlanData?.priceSubtext
    ? currentPlanData.priceSubtext.replace('/', '')
    : subscriptionStatus?.billing_cycle ?? '';

  // Loading state for different actions
  const [loadingAction, setLoadingAction] = useState<null | 'activate' | 'cancel' | 'refresh' | string>(null);
  const [showCancelConfirm, setShowCancelConfirm] = useState(false);

  // Check if user is in trial period
  const isTrialing = subscriptionStatus?.subscription_status === 'trialing';

  const handleCancel = async () => {
    // Show confirmation popup only for trial period
    if (isTrialing) {
      setShowCancelConfirm(true);
      return;
    }

    // Direct cancellation for paid subscriptions
    await performCancellation();
  };

  const performCancellation = async () => {
    setLoadingAction('cancel');
    try {
      const res = await fetch('/api/subscription/cancel', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ userId: user?.id })
      });
      const data = await res.json();
      if (res.ok) {
        toast.success('Subscription will cancel at period end');
        await refreshSubscriptionStatus();
      } else {
        toast.error(data.error || 'Failed to cancel subscription');
      }
    } catch (error) {
      console.error('Cancel error:', error);
      toast.error('Error canceling subscription');
    } finally {
      setLoadingAction(null);
      setShowCancelConfirm(false);
    }
  };

  const handleConfirmCancel = () => {
    performCancellation();
  };

  // const handleUpgrade = async () => {
  //   setLoadingAction('activate');
  //   try {
  //     const res = await fetch('/api/subscription/activate', {
  //       method: 'POST',
  //       headers: { 'Content-Type': 'application/json' },
  //       body: JSON.stringify({ userId: user?.id })
  //     });
      
  //     const data = await res.json();
  //     console.log('Activation response:', { status: res.status, data });
      
  //     if (res.ok) {
  //       if (data.success) {
  //         if (data.subscriptionStatus === 'active') {
  //           toast.success('🎉 Subscription activated successfully!');
  //           // Immediate refresh for active status
  //           await refreshSubscriptionStatus();
  //         } else if (data.subscriptionStatus === 'processing') {
  //           toast.success('⏳ Subscription is being processed. Refreshing status...');
  //           // Wait a bit longer for processing status
  //           setTimeout(async () => {
  //             await refreshSubscriptionStatus();
  //           }, 3000);
  //         } else if (data.subscriptionStatus === 'requires_action') {
  //           toast.warning('⚠️ Additional action required. Please check your payment method.');
  //         }
  //       } else {
  //         console.error('Activation failed:', data);
  //         toast.error(data.message || data.error || 'Activation failed');
  //       }
  //     } else {
  //       console.error('API error:', data);
  //       toast.error(data.error || 'Failed to activate subscription');
  //     }
  //   } catch (err) {
  //     console.error('Activate error', err);
  //     toast.error('Network error. Please try again.');
  //   } finally {
  //     setLoadingAction(null);
  //   }
  // };

  const handlePlanUpgrade = async (newPlanId: string) => {
    setLoadingAction(newPlanId); // Set loading to specific plan ID
    try {
      if (!subscriptionStatus) return;
      
      const res = await fetch('/api/subscription/update', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ 
          userId: user?.id, 
          newPlanId,
          immediate: true // Flag to indicate immediate upgrade
        }),
      });
      
      const data = await res.json();
      if (res.ok) {
        toast.success(`Successfully upgraded to ${newPlanId === 'quarterly-tier' ? 'Quarterly' : 'Annual'} plan!`);
        await refreshSubscriptionStatus();
      } else {
        toast.error(data.error || 'Upgrade failed');
      }
    } catch (error) {
      console.error('Upgrade error:', error);
      toast.error('Upgrade failed');
    } finally {
      setLoadingAction(null);
    }
  };

  const handleRefresh = async () => {
    setLoadingAction('refresh');
    try {
      await refreshSubscriptionStatus();
      toast.success('Subscription status refreshed');
    } catch (error) {
      toast.error('Failed to refresh status');
      console.error('Refresh error:', error);
    } finally {
      setLoadingAction(null);
    }
  };

  const upgradeOptions = () => {
    if (!subscriptionStatus || isTrialing) return [];
    if (subscriptionStatus.plan_id === 'monthly-tier') {
      return [
        { planId: 'quarterly-tier', label: 'Upgrade to Quarterly Plan' },
        { planId: 'annual-tier', label: 'Upgrade to Annual Plan' }
      ];
    }
    if (subscriptionStatus.plan_id === 'quarterly-tier') {
      return [
        { planId: 'annual-tier', label: 'Upgrade to Annual Plan' }
      ];
    }
    return [];
  };

  return (
    <section className="mb-8">
      <h2 className="text-2xl font-bold mb-4">Your Subscription</h2>
      
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle>Subscription Status</CardTitle>
              <CardDescription>
                Manage your Little Spark subscription
              </CardDescription>
            </div>
            <button
              onClick={handleRefresh}
              disabled={loadingAction !== null}
              className={`p-2 rounded-full hover:bg-gray-100 transition-colors ${
                loadingAction === 'refresh' ? 'animate-spin' : ''
              }`}
              title="Refresh subscription status"
            >
              <RefreshCw className="h-5 w-5 text-gray-500" />
            </button>
          </div>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {isLoading ? (
              <div className="animate-pulse">
                <div className="h-4 bg-gray-200 rounded w-3/4 mb-3"></div>
                <div className="h-4 bg-gray-200 rounded w-1/2 mb-3"></div>
                <div className="h-4 bg-gray-200 rounded w-2/3 mb-3"></div>
                <div className="h-4 bg-gray-200 rounded w-1/3"></div>
              </div>
            ) : subscriptionStatus === null ? (
              <p>Unable to load subscription details. Please try refreshing.</p>
            ) : !hasActiveSubscription ? (
              <div className="text-center py-8">
                <div className="space-y-4">
                  <p className="text-lg text-gray-600">No subscription</p>
                  <Link
                    href="/pricing"
                    className="inline-flex items-center gap-2 px-6 py-3 rounded-lg font-medium text-white
                      bg-gradient-to-r from-littlespark-teal to-blue-500
                      hover:from-littlespark-teal/90 hover:to-blue-600
                      transition-all duration-200"
                  >
                    Get Subscription
                  </Link>
                </div>
              </div>
            ) : (
              <div className="space-y-2">
                {/* Enhanced status display with trial expiration info */}
                <div className="flex items-center gap-2">
                  <p><strong>Status:</strong></p>
                  <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                    isTrialExpired
                      ? 'bg-red-100 text-red-800'
                      : subscriptionStatus.subscription_status === 'active'
                      ? 'bg-green-100 text-green-800'
                      : subscriptionStatus.subscription_status === 'trialing'
                      ? 'bg-blue-100 text-blue-800'
                      : 'bg-gray-100 text-gray-800'
                  }`}>
                    {isTrialExpired ? 'Trial Expired' :
                     subscriptionStatus.subscription_status === 'incomplete' ? 'Active' :
                     subscriptionStatus.subscription_status}
                  </span>
                </div>

                <p><strong>Plan:</strong> {subscriptionStatus.plan_name}</p>

                {/* Trial information with expiration warning */}
                {subscriptionStatus.subscription_status === 'trialing' ? (
                  <>
                    <p><strong>Trial Start:</strong> {subscriptionStatus.trial_start ? new Date(subscriptionStatus.trial_start).toLocaleDateString() : 'N/A'}</p>
                    <p><strong>Trial End:</strong> {subscriptionStatus.trial_end ? new Date(subscriptionStatus.trial_end).toLocaleDateString() : 'N/A'}</p>

                    {isTrialExpired && (
                      <div className="p-3 bg-red-50 rounded-lg border border-red-200 my-4">
                        <p className="text-sm text-red-800 font-medium">⚠️ Your trial has expired!</p>
                        <p className="text-xs text-red-700">
                          Please update your payment method or subscribe to continue using Little Spark.
                        </p>
                      </div>
                    )}

                    {!isTrialExpired && daysUntilExpiry !== undefined && daysUntilExpiry <= 3 && (
                      <div className="p-3 bg-yellow-50 rounded-lg border border-yellow-200 my-4">
                        <p className="text-sm text-yellow-800 font-medium">
                          ⏰ Trial ending in {daysUntilExpiry} day{daysUntilExpiry !== 1 ? 's' : ''}!
                        </p>
                        <p className="text-xs text-yellow-700">
                          Your subscription will automatically start after the trial ends.
                        </p>
                      </div>
                    )}
                  </>
                ) : (
                  <>
                    <p><strong>Start Date:</strong> {subscriptionStatus.subscription_start ? new Date(subscriptionStatus.subscription_start).toLocaleDateString() : 'N/A'}</p>
                    <p><strong>End Date:</strong> {subscriptionStatus.subscription_end ? new Date(subscriptionStatus.subscription_end).toLocaleDateString() : 'N/A'}</p>
                  </>
                )}

                <p><strong>Billing Cycle:</strong> {billingCycleText}</p>

                {subscriptionStatus.subscription_status === 'cancel_at_period_end' && (
                  <div className="p-3 bg-yellow-50 rounded-lg border border-yellow-200 my-4">
                    <p className="text-sm text-yellow-800 font-medium">Your subscription has been canceled.</p>
                    <p className="text-xs text-yellow-700">
                      You will retain access until{' '}
                      {subscriptionStatus.trial_end
                        ? new Date(subscriptionStatus.trial_end).toLocaleDateString()
                        : subscriptionStatus.subscription_end
                        ? new Date(subscriptionStatus.subscription_end).toLocaleDateString()
                        : 'the end of the period'}.
                    </p>
                  </div>
                )}

                {/* Show access result information if available */}
                {accessResult && !accessResult.hasAccess && (
                  <div className="p-3 bg-red-50 rounded-lg border border-red-200 my-4">
                    <p className="text-sm text-red-800 font-medium">⚠️ Access Restricted</p>
                    <p className="text-xs text-red-700">
                      {accessResult.reason}
                    </p>
                  </div>
                )}
              </div>
            )}
            
            <div className="space-y-2">
              {/* Show manage/cancel options only when there is an actionable change */}
              {(!hasActiveSubscription || subscriptionStatus?.plan_id !== 'annual-tier') && (
                <p className="text-sm text-gray-600">
                  Need to update your billing information, cancel, or modify your subscription?
                </p>
              )}

              {/* Show next renewal date and manage billing for active subscriptions */}
              {hasActiveSubscription && subscriptionStatus?.subscription_end && (
                <p className="text-sm text-gray-600">
                  <strong>Next Renewal:</strong> {new Date(subscriptionStatus.subscription_end).toLocaleDateString()}
                </p>
              )}
              {hasActiveSubscription && (
                <button
                  onClick={async () => {
                    setLoadingAction('portal');
                    try {
                      const res = await fetch('/api/stripe/customer-portal', {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify({ userId: user?.id })
                      });
                      const data = await res.json();
                      if (res.ok && data.url) {
                        window.location.href = data.url;
                      } else {
                        toast.error(data.error || 'Failed to open billing portal');
                      }
                    } catch (err) {
                      console.error('Portal error:', err);
                      toast.error('Failed to open billing portal');
                    } finally {
                      setLoadingAction(null);
                    }
                  }}
                  disabled={loadingAction === 'portal'}
                  className={`inline-flex items-center gap-2 px-6 py-3 rounded-xl font-semibold text-white bg-gradient-to-r from-blue-500 to-indigo-600 hover:from-blue-600 hover:to-indigo-700 transform hover:scale-105 transition-all duration-200 shadow-lg hover:shadow-xl ${loadingAction === 'portal' ? 'opacity-50 cursor-not-allowed' : ''}`}
                >
                  {loadingAction === 'portal' ? 'Loading...' : 'Manage Billing'}
                </button>
              )}
               {/* No subscription → Offer to purchase (duplicate removed) */}
              
              {/* Show upgrade button only if user is below the highest tier */}
              {hasActiveSubscription && !isTrialing && upgradeOptions().map(({ planId, label }) => (
                <button
                  key={planId}
                  onClick={() => handlePlanUpgrade(planId)}
                  disabled={loadingAction !== null}
                  className={`inline-flex items-center gap-2 px-6 py-3 rounded-xl font-semibold text-white
                    bg-gradient-to-r from-littlespark-teal to-blue-500 
                    hover:from-littlespark-teal/90 hover:to-blue-600 
                    transform hover:scale-105 transition-all duration-200 
                    shadow-lg hover:shadow-xl mr-3
                    ${loadingAction !== null ? 'opacity-50 cursor-not-allowed transform-none' : ''}`}
                >
                  <span>{loadingAction === planId ? 'Upgrading...' : label}</span>
                  {loadingAction !== planId && <span className="text-lg">⬆️</span>}
                </button>
              ))}
              
              {/* Trial actions */}
              {isTrialing && (
                <div className="flex flex-wrap gap-3 mt-4">
                  {/* Commented out activate button - no need to activate during trial period */}
                  {/* <button
                    onClick={handleUpgrade}
                    disabled={loadingAction !== null}
                    className={`inline-flex items-center gap-2 px-6 py-3 rounded-xl font-semibold text-white
                      bg-gradient-to-r from-green-500 to-emerald-600 
                      hover:from-green-600 hover:to-emerald-700 
                      transform hover:scale-105 transition-all duration-200 
                      shadow-lg hover:shadow-xl
                      ${loadingAction === 'activate' ? 'opacity-50 cursor-not-allowed transform-none' : ''}`}
                  >
                    <span>{loadingAction === 'activate' ? 'Activating...' : 'Activate Subscription Now'}</span>
                    {loadingAction !== 'activate' && <span className="text-lg">✨</span>}
                  </button> */}
                  
                  <button
                    onClick={handleCancel}
                    disabled={loadingAction !== null}
                    className={`inline-flex items-center gap-2 px-6 py-3 rounded-xl font-semibold
                      bg-gradient-to-r from-red-500 to-red-600 text-white
                      hover:from-red-600 hover:to-red-700 
                      transform hover:scale-105 transition-all duration-200 
                      shadow-lg hover:shadow-xl
                      ${loadingAction === 'cancel' ? 'opacity-50 cursor-not-allowed transform-none' : ''}`}
                  >
                    <span>{loadingAction === 'cancel' ? 'Cancelling...' : 'Cancel Subscription'}</span>
                    {loadingAction !== 'cancel' && <span className="text-lg">❌</span>}
                  </button>
                </div>
              )}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Confirmation dialog for trial cancellation only */}
      <ConfirmationDialog
        open={showCancelConfirm}
        onOpenChange={setShowCancelConfirm}
        title="Cancel Your Free Trial?"
        description="⚠️ Warning: Cancelling your trial will permanently mark it as used. You'll lose access to all premium features immediately and will need to purchase a subscription to continue. You cannot start another trial after cancelling."
        confirmText="Yes, Cancel Trial"
        cancelText="Keep Trial"
        onConfirm={handleConfirmCancel}
        variant="destructive"
        isLoading={loadingAction === 'cancel'}
      />
    </section>
  );
}

export default SubscriptionSection;
