import { useState, useEffect, useCallback, useRef } from 'react';

interface CacheEntry<T> {
  data: T;
  timestamp: number;
  isLoading: boolean;
}

interface ApiCacheOptions {
  cacheTime?: number; // Cache duration in milliseconds (default: 5 minutes)
  staleTime?: number; // Time before data is considered stale (default: 1 minute)
  refetchOnWindowFocus?: boolean; // Refetch when window gains focus (default: false)
}

class ApiCache {
  private cache = new Map<string, CacheEntry<unknown>>();
  private subscribers = new Map<string, Set<() => void>>();
  private focusListenerAdded = false;

  constructor() {
    // Add window focus listener only once
    if (typeof window !== 'undefined' && !this.focusListenerAdded) {
      window.addEventListener('focus', this.handleWindowFocus.bind(this));
      this.focusListenerAdded = true;
    }
  }

  private handleWindowFocus() {
    // Only refetch data that has refetchOnWindowFocus enabled
    // This is handled in the useApiCache hook
  }

  subscribe(key: string, callback: () => void) {
    if (!this.subscribers.has(key)) {
      this.subscribers.set(key, new Set());
    }
    this.subscribers.get(key)!.add(callback);

    return () => {
      const subs = this.subscribers.get(key);
      if (subs) {
        subs.delete(callback);
        if (subs.size === 0) {
          this.subscribers.delete(key);
        }
      }
    };
  }

  private notify(key: string) {
    const subs = this.subscribers.get(key);
    if (subs) {
      subs.forEach(callback => callback());
    }
  }

  get<T>(key: string): CacheEntry<T> | undefined {
    return this.cache.get(key) as CacheEntry<T> | undefined;
  }

  set<T>(key: string, data: T, isLoading = false) {
    this.cache.set(key, {
      data,
      timestamp: Date.now(),
      isLoading
    });
    this.notify(key);
  }

  setLoading(key: string, isLoading: boolean) {
    const entry = this.cache.get(key);
    if (entry) {
      entry.isLoading = isLoading;
      this.notify(key);
    }
  }

  isStale(key: string, staleTime: number): boolean {
    const entry = this.cache.get(key);
    if (!entry) return true;
    return Date.now() - entry.timestamp > staleTime;
  }

  isExpired(key: string, cacheTime: number): boolean {
    const entry = this.cache.get(key);
    if (!entry) return true;
    return Date.now() - entry.timestamp > cacheTime;
  }

  invalidate(key: string) {
    this.cache.delete(key);
    this.notify(key);
  }

  clear() {
    this.cache.clear();
    this.subscribers.clear();
  }
}

// Global cache instance
const globalCache = new ApiCache();

export function useApiCache<T>(
  key: string,
  fetcher: () => Promise<T>,
  options: ApiCacheOptions = {}
) {
  const {
    cacheTime = 5 * 60 * 1000, // 5 minutes
    staleTime = 60 * 1000, // 1 minute
    refetchOnWindowFocus = false
  } = options;

  const [data, setData] = useState<T | undefined>();
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<Error | null>(null);
  const fetcherRef = useRef(fetcher);
  const lastFocusTime = useRef(0);

  // Update fetcher ref
  fetcherRef.current = fetcher;

  const fetchData = useCallback(async (force = false) => {
    // Don't fetch if key is empty (user not loaded)
    if (!key) {
      setIsLoading(false);
      return;
    }

    const cacheEntry = globalCache.get<T>(key);

    // Return cached data if not expired and not forced
    if (!force && cacheEntry && !globalCache.isExpired(key, cacheTime)) {
      setData(cacheEntry.data);
      setIsLoading(cacheEntry.isLoading);
      return cacheEntry.data;
    }

    try {
      globalCache.setLoading(key, true);
      setIsLoading(true);
      setError(null);

      const result = await fetcherRef.current();

      globalCache.set(key, result, false);
      setData(result);
      return result;
    } catch (err) {
      const error = err instanceof Error ? err : new Error('Unknown error');
      setError(error);
      globalCache.setLoading(key, false);
      throw error;
    } finally {
      setIsLoading(false);
    }
  }, [key, cacheTime]);

  const refetch = useCallback(() => {
    return fetchData(true);
  }, [fetchData]);

  const invalidate = useCallback(() => {
    globalCache.invalidate(key);
    setData(undefined);
    setError(null);
  }, [key]);

  // Subscribe to cache changes
  useEffect(() => {
    const unsubscribe = globalCache.subscribe(key, () => {
      const cacheEntry = globalCache.get<T>(key);
      if (cacheEntry) {
        setData(cacheEntry.data);
        setIsLoading(cacheEntry.isLoading);
      }
    });

    return unsubscribe;
  }, [key]);

  // Initial fetch
  useEffect(() => {
    // Don't fetch if key is empty (user not loaded)
    if (!key) {
      setIsLoading(false);
      return;
    }

    const cacheEntry = globalCache.get<T>(key);

    if (!cacheEntry) {
      // No cache, fetch immediately
      fetchData();
    } else if (globalCache.isStale(key, staleTime)) {
      // Cache is stale, fetch in background
      fetchData();
    } else {
      // Use cached data
      setData(cacheEntry.data);
      setIsLoading(cacheEntry.isLoading);
    }
  }, [key, fetchData, staleTime]);

  // Handle window focus refetch
  useEffect(() => {
    if (!refetchOnWindowFocus || !key) return;

    const handleFocus = () => {
      const now = Date.now();
      // Prevent rapid refetches on focus
      if (now - lastFocusTime.current > 1000) {
        lastFocusTime.current = now;
        if (globalCache.isStale(key, staleTime)) {
          fetchData();
        }
      }
    };

    window.addEventListener('focus', handleFocus);
    return () => window.removeEventListener('focus', handleFocus);
  }, [key, staleTime, fetchData, refetchOnWindowFocus]);

  return {
    data,
    isLoading,
    error,
    refetch,
    invalidate,
    isStale: data ? globalCache.isStale(key, staleTime) : true
  };
}

export { globalCache };
