import React, { useEffect } from "react";
import Achievements from "@/components/dashboard/Achievements";
import ProgressDashboard from "@/components/ProgressDashboard";
import { useAuth } from "@/hooks/useAuth";
import { useApiCache } from "@/hooks/useApiCache";

interface UserProgressProps {
    isLoading: boolean;
    userData: {
        completedProjects: number;
        skillLevel: number;
        skillProgress?: number;
        streakDays: number;
        badges: Array<{
            name: string;
            icon: string;
            earned: boolean;
        }>;
    };
}

const UserProgress = ({
    userData: initialUserData,
}: UserProgressProps) => {
    const { user } = useAuth();

    // Use cached API call for user progress
    const {
        data: progressData,
        isLoading,
        refetch: fetchRealUserData
    } = useApiCache(
        user?.id ? `user-progress-${user.id}` : '',
        async () => {
            if (!user?.id) throw new Error('No user ID');

            const response = await fetch('/api/user-progress');
            const data = await response.json();

            if (!data.success) {
                throw new Error(data.error || 'Failed to fetch user progress');
            }

            return {
                completedProjects: data.progress.completedProjects,
                skillLevel: data.progress.skillLevel,
                skillProgress: data.progress.skillProgress,
                streakDays: data.progress.streakDays,
                badges: data.progress.badges
            };
        },
        {
            cacheTime: 3 * 60 * 1000, // 3 minutes cache
            staleTime: 60 * 1000, // 1 minute stale time
            refetchOnWindowFocus: false
        }
    );

    // Use cached data or fallback to initial data
    const userData = progressData || initialUserData;

    // Listen for challenge completion and content save events to update progress in real-time
    useEffect(() => {
        const handleChallengeCompleted = (event: CustomEvent) => {
            console.log('Challenge completed, updating progress...', event.detail);
            // Refresh user data when a challenge is completed
            fetchRealUserData();
        };

        const handleContentSaved = (event: CustomEvent) => {
            console.log('Content saved, updating progress...', event.detail);
            // Refresh user data when content is saved
            fetchRealUserData();
        };

        window.addEventListener('challengeCompleted', handleChallengeCompleted as EventListener);
        window.addEventListener('contentSaved', handleContentSaved as EventListener);

        return () => {
            window.removeEventListener('challengeCompleted', handleChallengeCompleted as EventListener);
            window.removeEventListener('contentSaved', handleContentSaved as EventListener);
        };
    }, [fetchRealUserData]);

    return (
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 sm:gap-6 lg:gap-8">
            <Achievements isLoading={isLoading} userData={userData} />
            <ProgressDashboard
                completedProjects={userData.completedProjects}
                skillLevel={userData.skillLevel}
                skillProgress={userData.skillProgress}
                streakDays={userData.streakDays}
                badges={userData.badges}
            />
        </div>
    );
};

export default UserProgress;
