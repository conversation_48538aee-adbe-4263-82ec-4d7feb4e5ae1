"use client";
import React from 'react';
import MusicContent from './components/MusicContent';
import MusicLayout from './components/MusicLayout';

interface MusicContainerProps {
  challengeData?: {
    title: string;
    description: string;
    category: string;
    instructions?: string;
    learningObjectives?: Array<{objective: string}>;
    materials?: Array<{material: string}>;
    estimatedTime?: number;
    difficulty?: string;
  };
}

const MusicContainer = ({ challengeData }: MusicContainerProps) => {
  const [learningMode, setLearningMode] = React.useState(true);

  const toggleLearningMode = () => {
    setLearningMode(!learningMode);
  };

  return (
    <div className="min-h-screen bg-gradient-to-b from-white to-spark-lavender/5 py-8 px-4">
      <div className="container mx-auto max-w-6xl">
        {/* Challenge Information */}
        {challengeData && (
          <div className="bg-purple-50 border border-purple-200 rounded-lg p-6 mb-6">
            <h2 className="text-xl font-bold text-purple-900 mb-2">
              🎵 Challenge: {challengeData.title}
            </h2>
            <p className="text-purple-800 mb-3">{challengeData.description}</p>

            <div className="grid md:grid-cols-2 gap-4 text-sm">
              <div>
                <h4 className="font-semibold text-purple-900 mb-2">Instructions:</h4>
                <p className="text-purple-700 whitespace-pre-line">{challengeData.instructions}</p>
              </div>

              <div>
                <h4 className="font-semibold text-purple-900 mb-2">Learning Goals:</h4>
                <ul className="text-purple-700 space-y-1">
                  {challengeData.learningObjectives?.map((obj: {objective: string}, index: number) => (
                    <li key={index}>• {obj.objective}</li>
                  ))}
                </ul>

                {challengeData.materials && challengeData.materials.length > 0 && (
                  <div className="mt-3">
                    <h4 className="font-semibold text-purple-900 mb-2">Materials:</h4>
                    <ul className="text-purple-700 space-y-1">
                      {challengeData.materials.map((mat: {material: string}, index: number) => (
                        <li key={index}>• {mat.material}</li>
                      ))}
                    </ul>
                  </div>
                )}
              </div>
            </div>

            <div className="mt-4 flex items-center gap-4 text-sm text-purple-600">
              <span>⏱️ {challengeData.estimatedTime} minutes</span>
              <span>📊 {challengeData.difficulty}</span>
            </div>
          </div>
        )}

        <MusicLayout
          learningMode={learningMode}
          toggleLearningMode={toggleLearningMode}
        >
          <MusicContent />
        </MusicLayout>
      </div>
    </div>
  );
};

export default MusicContainer;
