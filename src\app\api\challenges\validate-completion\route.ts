import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { createServerSupabaseClient } from '@/lib/supabase/server';

// POST /api/challenges/validate-completion - Validate if user completed required content
export async function POST(request: NextRequest) {
  try {
    // Get authenticated user
    const supabase = await createServerSupabaseClient();
    const { data: { user } } = await supabase.auth.getUser();
    
    if (!user) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Authentication required' 
        },
        { status: 401 }
      );
    }

    const { challengeId, challengeStartedAt } = await request.json();

    if (!challengeId) {
      return NextResponse.json(
        {
          success: false,
          error: 'Challenge ID is required'
        },
        { status: 400 }
      );
    }

    // Convert challengeId to string if it's a number
    const challengeIdString = String(challengeId);

    // Get challenge details
    const challenge = await prisma.challenge.findUnique({
      where: { id: challengeIdString }
    });

    if (!challenge) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Challenge not found' 
        },
        { status: 404 }
      );
    }

    // Check if user already completed this challenge
    const existingCompletion = await prisma.challengeCompletion.findUnique({
      where: {
        user_id_challenge_id: {
          user_id: user.id,
          challenge_id: challengeIdString
        }
      }
    });

    if (existingCompletion) {
      return NextResponse.json({
        success: true,
        canComplete: true,
        alreadyCompleted: true,
        message: 'Challenge already completed!'
      });
    }

    // Validate challenge start timestamp
    if (!challengeStartedAt) {
      return NextResponse.json({
        success: true,
        canComplete: false,
        message: "This challenge is not completed yet. Please start the challenge and create the required content.",
        requiredType: challenge.type,
        challengeTitle: challenge.title
      });
    }

    const challengeStartTime = new Date(challengeStartedAt);

    // Enhanced validation: Look for user content that matches the challenge type AND was created AFTER starting the challenge
    // Also ensure minimum time gap to prevent rapid completion abuse
    // const minimumWorkTime = 2 * 60 * 1000; // 2 minutes minimum work time
    // const earliestValidTime = new Date(challengeStartTime.getTime() + minimumWorkTime);

    const matchingContent = await prisma.userContent.findFirst({
      where: {
        user_id: user.id,
        type: challenge.type,
        created_at: {
          gte: challengeStartTime, // Content must be created after challenge was started
          // Optional: Add minimum work time validation for more security
          // gte: earliestValidTime
        },
        // Prefer content specifically created for this challenge
        OR: [
          { challenge_id: challengeIdString },
          {
            AND: [
              { challenge_id: null },
              { created_at: { gte: challengeStartTime } }
            ]
          }
        ]
      },
      orderBy: [
        { challenge_id: 'desc' }, // Prioritize content created for this specific challenge
        { created_at: 'desc' }
      ]
    });

    // Additional validation: Check if content was created too quickly (potential abuse)
    if (matchingContent) {
      const timeDifference = matchingContent.created_at.getTime() - challengeStartTime.getTime();
      // const minimumWorkTime = 2 * 60 * 1000; // 2 minutes minimum work time
      // const isQuickCompletion = timeDifference < minimumWorkTime;

      // Log suspicious activity but don't block (for now)
      // if (isQuickCompletion) {
        console.log(`Quick completion detected for user ${user.id}, challenge ${challengeIdString}: ${timeDifference}ms`);
      // }
    }

    if (!matchingContent) {
      return NextResponse.json({
        success: true,
        canComplete: false,
        message: "This challenge is not completed yet. Please start the challenge and create the required content.",
        requiredType: challenge.type,
        challengeTitle: challenge.title,
        challengeStartedAt: challengeStartedAt
      });
    }

    // User has matching content, they can complete the challenge
    return NextResponse.json({
      success: true,
      canComplete: true,
      contentFound: {
        id: matchingContent.id,
        title: matchingContent.title,
        type: matchingContent.type,
        createdAt: matchingContent.created_at
      },
      message: 'Great! You can mark this challenge as complete.'
    });

  } catch (error) {
    console.error('Error validating challenge completion:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to validate challenge completion' 
      },
      { status: 500 }
    );
  }
}
