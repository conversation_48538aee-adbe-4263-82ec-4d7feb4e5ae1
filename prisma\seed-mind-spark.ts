import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

const mindSparkQuestions = [
  // Age Group: 5-7 (Early Elementary)
  {
    question: "If you could have any superpower, what would it be and how would you use it to help others?",
    age_group: "5-7",
    difficulty: "easy",
    category: "creativity",
    is_ai_generated: false
  },
  {
    question: "What do you think clouds are made of? Can you think of three different ideas?",
    age_group: "5-7",
    difficulty: "easy",
    category: "science",
    is_ai_generated: false
  },

  // Age Group: 8-10 (Elementary)
  {
    question: "If you could design a new playground, what special features would you include to make it fun for everyone?",
    age_group: "8-10",
    difficulty: "medium",
    category: "creativity",
    is_ai_generated: false
  },
  {
    question: "Why do you think some animals hibernate in winter while others don't? What might be the advantages?",
    age_group: "8-10",
    difficulty: "medium",
    category: "science",
    is_ai_generated: false
  },

  // Age Group: 11-13 (Middle School)
  {
    question: "If you could solve one problem in your school or community, what would it be and what steps would you take?",
    age_group: "11-13",
    difficulty: "medium",
    category: "logic",
    is_ai_generated: false
  },
  {
    question: "How do you think social media affects friendships? What are the positive and negative aspects?",
    age_group: "11-13",
    difficulty: "medium",
    category: "general",
    is_ai_generated: false
  },

  // Age Group: 14-16 (High School)
  {
    question: "If you could start your own business, what problem would it solve and how would you make it sustainable?",
    age_group: "14-16",
    difficulty: "hard",
    category: "logic",
    is_ai_generated: false
  },
  {
    question: "How do you think artificial intelligence will change the job market in the next 10 years? What skills should people focus on?",
    age_group: "14-16",
    difficulty: "hard",
    category: "general",
    is_ai_generated: false
  },

  // Age Group: 17+ (Young Adult)
  {
    question: "What role do you think young people should play in addressing climate change? How can individual actions create collective impact?",
    age_group: "17+",
    difficulty: "hard",
    category: "general",
    is_ai_generated: false
  },
  {
    question: "If you could redesign the education system, what changes would you make to better prepare students for the future?",
    age_group: "17+",
    difficulty: "hard",
    category: "creativity",
    is_ai_generated: false
  }
];

async function seedMindSparkQuestions() {
  console.log('🌱 Seeding Mind Spark questions...');

  try {
    // Clear existing questions (optional - remove if you want to keep existing data)
    await prisma.mindSparkQuestion.deleteMany({
      where: { is_ai_generated: false }
    });

    // Insert hardcoded questions
    for (const questionData of mindSparkQuestions) {
      await prisma.mindSparkQuestion.create({
        data: questionData
      });
    }

    console.log(`✅ Successfully seeded ${mindSparkQuestions.length} Mind Spark questions!`);
    
    // Show summary by age group
    const summary = await prisma.mindSparkQuestion.groupBy({
      by: ['age_group'],
      _count: {
        id: true
      },
      where: { is_ai_generated: false }
    });

    console.log('\n📊 Questions by age group:');
    summary.forEach((group: { age_group: string; _count: { id: number } }) => {
      console.log(`  ${group.age_group}: ${group._count.id} questions`);
    });

  } catch (error) {
    console.error('❌ Error seeding Mind Spark questions:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

// Run the seed function
if (require.main === module) {
  seedMindSparkQuestions()
    .catch((error) => {
      console.error(error);
      process.exit(1);
    });
}

export { seedMindSparkQuestions };
