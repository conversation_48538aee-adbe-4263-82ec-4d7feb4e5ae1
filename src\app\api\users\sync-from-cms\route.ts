import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';

// Helper function to verify sync token
function verifySyncToken(req: NextRequest) {
  const authHeader = req.headers.get('authorization');
  const expectedToken = process.env.CMS_SYNC_TOKEN;
  
  if (!authHeader || !authHeader.startsWith('Bearer ') || !expectedToken) {
    return false;
  }
  
  const token = authHeader.substring(7);
  return token === expectedToken;
}

// POST /api/users/sync-from-cms - Receive synced user from CMS
export async function POST(request: NextRequest) {
  try {
    // Verify sync token
    if (!verifySyncToken(request)) {
      return NextResponse.json(
        { error: 'Unauthorized sync request' },
        { status: 401 }
      );
    }

    const userData = await request.json();
    
    console.log(`🔄 [MAIN-APP-SYNC] Receiving user from CMS: ${userData.email}`);

    // Validate required fields
    if (!userData.email || !userData.full_name) {
      return NextResponse.json(
        { error: 'Missing required fields: email, full_name' },
        { status: 400 }
      );
    }

    // Check if user already exists in profiles
    const existingProfile = await prisma.profile.findUnique({
      where: { email: userData.email }
    });

    if (existingProfile) {
      // Update existing user profile
      const updatedProfile = await prisma.profile.update({
        where: { id: existingProfile.id },
        data: {
          full_name: userData.full_name,
          bio: userData.bio,
          cms_user_id: userData.cms_user_id,
          is_cms_user: userData.is_cms_user,
          cms_role: userData.role,
          cms_specialties: userData.specialties,
          updated_at: new Date(),
        }
      });

      console.log(`✅ [MAIN-APP-SYNC] Updated user profile: ${userData.email}`);

      return NextResponse.json({
        success: true,
        action: 'updated',
        profile: updatedProfile
      });
    }

    // Create new profile (no separate user table in our schema)
    const newProfile = await prisma.profile.create({
      data: {
        id: userData.cms_user_id, // Use CMS user ID as profile ID
        email: userData.email,
        full_name: userData.full_name,
        bio: userData.bio,
        cms_user_id: userData.cms_user_id,
        is_cms_user: userData.is_cms_user,
        cms_role: userData.role,
        cms_specialties: userData.specialties,
        subscription_status: 'active', // CMS users get active status
        trial_used: true, // Skip trial for CMS users
      }
    });
    
    console.log(`🆕 [MAIN-APP-SYNC] Created user: ${userData.email}`);
    
    return NextResponse.json({
      success: true,
      action: 'created',
      profile: newProfile
    });

  } catch (error) {
    console.error('❌ [MAIN-APP-SYNC] Error syncing user from CMS:', error);
    return NextResponse.json(
      { 
        success: false,
        error: 'Failed to sync user from CMS',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
