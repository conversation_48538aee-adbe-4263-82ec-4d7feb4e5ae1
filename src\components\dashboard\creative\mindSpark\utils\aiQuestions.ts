import { AgeGroup, Question } from '../types';

// TODO: Replace this with your custom AI API endpoint
const callCustomAIService = async (ageGroup: AgeGroup, count: number): Promise<Question[] | null> => {
  // Placeholder for future custom backend integration
  // Replace this with your actual API call
  try {
    // Example of what the custom API call might look like:
    // const response = await fetch('/api/ai/generate-questions', {
    //   method: 'POST',
    //   headers: { 'Content-Type': 'application/json' },
    //   body: JSON.stringify({
    //     prompt: `Generate ${count} questions for age group ${ageGroup}`,
    //     type: 'mindspark-questions',
    //     ageGroup: ageGroup,
    //     count: count
    //   })
    // });
    // 
    // if (!response.ok) throw new Error('API call failed');
    // const data = await response.json();
    // return data.questions;

    console.log(`[PLACEHOLDER] Would generate ${count} AI questions for age group ${ageGroup}`);
    return null; // Return null to trigger fallback to static questions
  } catch (error) {
    console.error('Error calling custom AI service:', error);
    return null;
  }
};

// Get AI-generated questions
export const getQuestionsWithAI = async (ageGroup: AgeGroup, count: number = 1): Promise<Question[]> => {
  try {
    // TODO: Replace with your custom backend call
    const aiQuestions = await callCustomAIService(ageGroup, count);
    
    // If we have valid data from the AI service, return it
    if (aiQuestions && Array.isArray(aiQuestions) && aiQuestions.length > 0) {
      return aiQuestions;
    } else {
      console.log('AI service not available or returned invalid data, using fallback questions');
      // If AI fails or returns invalid data, fall back to static questions
      return getFallbackQuestions(ageGroup, count);
    }
  } catch (error) {
    console.error('Error in getQuestionsWithAI:', error);
    // In case of any error, fall back to static questions
    return getFallbackQuestions(ageGroup, count);
  }
};

// No fallback questions - purely AI generated
export const getFallbackQuestions = (ageGroup: AgeGroup, count: number): Question[] => {
  console.log(`No fallback questions available for age group ${ageGroup}. Requested ${count} questions.`);
  return [];
};
