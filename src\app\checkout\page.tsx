"use client";

import React, { useEffect, useState, Suspense } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { toast } from "sonner";
import CheckoutLayout from "@/components/checkout/CheckoutLayout";
import CheckoutSummary from "@/components/checkout/CheckoutSummary";
import PlanSelector from "@/components/checkout/PlanSelector";
import StripePaymentForm from "@/components/checkout/StripePaymentForm";
import { useCheckout } from "@/hooks/checkout";
import { useAuth } from "@/hooks/useAuth";
import { useProfile } from "@/hooks/useProfile";
import { getPlanDetails } from "@/utils/checkout/planUtils";
import { useSubscriptionStatus } from "@/hooks/useSubscriptionStatus";
import { CreditCard, Shield, ArrowRight } from "lucide-react";

const CheckoutContent = () => {
    const router = useRouter();
    const { hasActiveSubscription, isLoading: subscriptionLoading } = useSubscriptionStatus();
    const searchParams = useSearchParams();
    const { user } = useAuth();
    const { profile } = useProfile();
    const [showPaymentForm, setShowPaymentForm] = useState(false);
    const [isUpgrade, setIsUpgrade] = useState(false);
    const {
        selectedPlanId,
        plans,
        handlePlanChange,
        isSubmitting,
        setIsSubmitting,
    } = useCheckout();
    const { subscriptionStatus } = useSubscriptionStatus();

    useEffect(() => {
        // Don't redirect while still loading subscription status
        if (subscriptionLoading) return;
        
        const isUpgrade = searchParams.get('upgrade') === '1';
        if (hasActiveSubscription && !isUpgrade) {
            toast.error("You already have an active subscription");
            router.push("/dashboard");
        }
    }, [hasActiveSubscription, router, searchParams, subscriptionLoading]);

    useEffect(() => {
        try {
            const planParam = searchParams.get("plan");
            const upgradeParam = searchParams.get('upgrade') === '1';
            setIsUpgrade(upgradeParam);

            // For upgrades, only show plans higher than current plan
            if (upgradeParam && hasActiveSubscription && subscriptionStatus?.plan_id) {
                const currentPlan = plans.find(p => p.planId === subscriptionStatus.plan_id);
                if (currentPlan) {
                    const currentPlanDetails = getPlanDetails(currentPlan.planId, plans);
                    const currentPrice = parseFloat(currentPlanDetails.monthlyPrice);
                    const availablePlans = plans.filter(p => {
                        const planDetails = getPlanDetails(p.planId, plans);
                        return parseFloat(planDetails.monthlyPrice) > currentPrice;
                    });
                    if (availablePlans.length > 0) {
                        handlePlanChange(availablePlans[0].planId);
                    }
                }
            }

            if (planParam && plans.some((plan) => plan.planId === planParam)) {
                handlePlanChange(planParam);
                setShowPaymentForm(true);
                // removed autoPay logic as it's unused
            } else if (!selectedPlanId && !upgradeParam) {
                handlePlanChange("annual-tier");
            }
        } catch (error) {
            console.error("Error processing checkout parameters:", error);
            toast.error(
                "There was an issue loading your plan selection. The default plan has been selected."
            );
            handlePlanChange("annual-tier");
        }
    }, [searchParams, plans, selectedPlanId, handlePlanChange, hasActiveSubscription, subscriptionStatus, subscriptionLoading]);

    const handleContinueToPayment = () => {
        if (!selectedPlanId) {
            toast.error("Please select a plan to continue");
            return;
        }
        // Direct redirect to payment form without any confirmation
        setShowPaymentForm(true);
    };

    const handlePaymentSuccess = () => {
        toast.success("Subscription activated! Welcome to Little Spark!");
        setTimeout(() => {
            router.push("/dashboard");
        }, 1500);
    };

    const handlePaymentError = (error: string) => {
        toast.error(`Payment failed: ${error}`);
        setIsSubmitting(false);
    };

    const handleBackToPlanSelection = () => {
        setShowPaymentForm(false);
        setIsSubmitting(false);
    };

    const getAvailablePlans = () => {
        if (!isUpgrade || !hasActiveSubscription || !subscriptionStatus?.plan_id) {
            return plans;
        }

        const currentPlan = plans.find(p => p.planId === subscriptionStatus.plan_id);
        if (!currentPlan) return plans;

        const currentPlanDetails = getPlanDetails(currentPlan.planId, plans);
        const currentPrice = parseFloat(currentPlanDetails.monthlyPrice);

        return plans.filter(p => {
            const planDetails = getPlanDetails(p.planId, plans);
            return parseFloat(planDetails.monthlyPrice) > currentPrice;
        });
    };

    const selectedPlanDetails = selectedPlanId ? getPlanDetails(selectedPlanId, plans) : null;

    return (
        <div className="min-h-screen bg-white">
            <CheckoutLayout>
                {!showPaymentForm ? (
                    // Plan Selection Step
                    <div className="grid md:grid-cols-2 gap-8">
                        <div>
                            {getAvailablePlans().length === 0 ? (
                                <div className="p-6 bg-yellow-50 rounded-lg border border-yellow-200">
                                    <h3 className="text-lg font-semibold text-yellow-800 mb-2">No Available Upgrades</h3>
                                    <p className="text-yellow-700">You are already on our highest tier plan. No upgrades are available at this time.</p>
                                </div>
                            ) : (
                                <PlanSelector
                                    plans={getAvailablePlans()}
                                    selectedPlanId={selectedPlanId || ""}
                                    setSelectedPlanId={handlePlanChange}
                                />
                            )}
                        </div>

                        <div>
                            {selectedPlanId && getAvailablePlans().length > 0 && (
                                <CheckoutSummary
                                    key={selectedPlanId}
                                    {...getPlanDetails(selectedPlanId, plans)}
                                />
                            )}

                            <div className="mt-6 p-4 bg-white rounded-lg shadow-sm border">
                                <div className="pt-2">
                                    <button
                                        onClick={handleContinueToPayment}
                                        disabled={isSubmitting}
                                        className="relative w-full h-14 px-8 py-3 rounded-xl text-white text-base font-bold 
                                                 bg-gradient-to-r from-littlespark-teal to-emerald-500 
                                                 hover:from-littlespark-teal/90 hover:to-emerald-600 
                                                 transform hover:scale-[1.02] transition-all duration-200 
                                                 shadow-lg hover:shadow-xl
                                                 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none
                                                 flex items-center justify-center gap-3
                                                 group overflow-hidden"
                                    >
                                        <div className="absolute inset-0 bg-gradient-to-r from-white/0 to-white/20 transform translate-x-[-100%] group-hover:translate-x-[100%] transition-transform duration-700"></div>
                                        <CreditCard className="h-5 w-5 relative z-10" />
                                        <span className="relative z-10">Continue</span>
                                        <ArrowRight className="h-5 w-5 relative z-10 group-hover:translate-x-1 transition-transform duration-200" />
                                    </button>
                                </div>

                                <div className="flex items-center justify-center gap-2 mt-4 text-sm text-gray-600">
                                    <Shield className="h-4 w-4" />
                                    <span>Secure payment powered by Stripe</span>
                                </div>
                            </div>
                        </div>
                    </div>
                ) : (
                    // Payment Form Step
                    <div className="max-w-2xl mx-auto">
                        <div className="mb-6">
                            <button
                                onClick={handleBackToPlanSelection}
                                className="flex items-center gap-2 text-gray-600 hover:text-gray-800 transition-colors"
                            >
                                <ArrowRight className="h-4 w-4 rotate-180" />
                                Back to plan selection
                            </button>
                        </div>

                        <div className="bg-white rounded-lg shadow-sm border p-6">
                            {selectedPlanDetails && (
                                <div className="mb-6 p-4 bg-gray-50 rounded-lg">
                                    <h3 className="font-semibold mb-2">Selected Plan</h3>
                                    <div className="flex justify-between">
                                        <span>{selectedPlanDetails.planName}</span>
                                        <span className="font-bold">${selectedPlanDetails.monthlyPrice}</span>
                                    </div>
                                </div>
                            )}

                            <StripePaymentForm
                                planId={selectedPlanId || ""}
                                onSuccess={handlePaymentSuccess}
                                onError={handlePaymentError}
                                userEmail={user?.email || ""}
                                userName={profile?.full_name || ""}
                            />
                        </div>
                    </div>
                )}
            </CheckoutLayout>
        </div>
    );
};

export default function CheckoutPage() {
    return (
        <Suspense fallback={<div>Loading checkout...</div>}>
            <CheckoutContent />
        </Suspense>
    );
}
