import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { createServerSupabaseClient } from '@/lib/supabase/server';
import { ChatGoogleGenerativeAI } from '@langchain/google-genai';

const GOOGLE_API_KEY = process.env.GOOGLE_API_KEY;

const llm = new ChatGoogleGenerativeAI({
  model: "gemini-2.0-flash",
  temperature: 0.7,
  apiKey: GOOGLE_API_KEY || "",
});

interface GenerateQuestionRequest {
  age_group: string;
  category?: string;
  difficulty?: string;
}

const ageGroupPrompts = {
  "5-7": {
    description: "5-7 year olds (early elementary)",
    characteristics: "curious, imaginative, learning basic concepts, love stories and simple explanations",
    examples: "What would happen if animals could talk? Why do you think the sky is blue?"
  },
  "8-10": {
    description: "8-10 year olds (elementary)",
    characteristics: "developing logical thinking, interested in how things work, can handle more complex ideas",
    examples: "How would you design a robot to help at home? What do you think makes a good friend?"
  },
  "11-13": {
    description: "11-13 year olds (middle school)",
    characteristics: "abstract thinking developing, interested in social issues, can analyze problems",
    examples: "How would you solve bullying in schools? What impact does technology have on communication?"
  },
  "14-16": {
    description: "14-16 year olds (high school)",
    characteristics: "advanced reasoning, interested in real-world applications, can think critically about complex topics",
    examples: "How would you address climate change in your community? What skills will be most important for future careers?"
  },
  "17+": {
    description: "17+ year olds (young adults)",
    characteristics: "mature thinking, ready for complex philosophical and practical questions, preparing for adult responsibilities",
    examples: "How should society balance individual freedom with collective responsibility? What role should young people play in shaping the future?"
  }
};

async function generateQuestionWithGemini(age_group: string, category: string, difficulty: string): Promise<string> {
  if (!GOOGLE_API_KEY) {
    throw new Error('Gemini API key not configured');
  }

  const ageInfo = ageGroupPrompts[age_group as keyof typeof ageGroupPrompts];
  if (!ageInfo) {
    throw new Error(`Invalid age group: ${age_group}`);
  }

  const prompt = `Generate a thought-provoking question for ${ageInfo.description}.

Target audience characteristics: ${ageInfo.characteristics}

Requirements:
- Category: ${category}
- Difficulty: ${difficulty}
- Age-appropriate language and concepts
- Encourages critical thinking and creativity
- Open-ended (no single correct answer)
- Engaging and relevant to their interests
- Should inspire thoughtful discussion

Examples of good questions for this age group: ${ageInfo.examples}

Generate ONE question only. Do not include any explanation or additional text - just the question itself.`;

  try {
    
    const response = await llm.invoke(prompt);
    console.log(response);  

    if (!response.content) {
      throw new Error('No text generated by Gemini');
    }

    return response.content;

  } catch (error) {
    console.error('Error calling Gemini API:', error);
    throw error;
  }
}

export async function POST(request: NextRequest) {
  try {
    // Get authenticated user
    const supabase = await createServerSupabaseClient();
    const { data: { user } } = await supabase.auth.getUser();
    
    if (!user) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Authentication required' 
        },
        { status: 401 }
      );
    }

    const body: GenerateQuestionRequest = await request.json();
    const { age_group, category = 'general', difficulty = 'medium' } = body;

    // Validate age group
    if (!ageGroupPrompts[age_group as keyof typeof ageGroupPrompts]) {
      return NextResponse.json(
        {
          success: false,
          error: 'Invalid age group. Must be one of: 5-7, 8-10, 11-13, 14-16, 17+'
        },
        { status: 400 }
      );
    }

    console.log(`🤖 [MIND-SPARK-GENERATE] Generating question for age group: ${age_group}, category: ${category}, difficulty: ${difficulty}`);

    // Generate question using Gemini
    const generatedQuestion = await generateQuestionWithGemini(age_group, category, difficulty);
    
    console.log(`✨ [MIND-SPARK-GENERATE] Generated question: ${generatedQuestion}`);

    // Save to database
    const savedQuestion = await prisma.mindSparkQuestion.create({
      data: {
        question: generatedQuestion,
        age_group,
        difficulty,
        category,
        is_ai_generated: true
      }
    });

    console.log(`💾 [MIND-SPARK-GENERATE] Saved question to database with ID: ${savedQuestion.id}`);

    return NextResponse.json({
      success: true,
      message: 'Question generated and saved successfully',
      question: {
        id: savedQuestion.id,
        question: savedQuestion.question,
        age_group: savedQuestion.age_group,
        category: savedQuestion.category,
        difficulty: savedQuestion.difficulty,
        is_ai_generated: savedQuestion.is_ai_generated,
        created_at: savedQuestion.created_at
      }
    });

  } catch (error) {
    console.error('❌ [MIND-SPARK-GENERATE] Error generating question:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to generate question',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
