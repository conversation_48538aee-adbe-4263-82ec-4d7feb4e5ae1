import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { createServerSupabaseClient } from '@/lib/supabase/server';
import { ChatGoogleGenerativeAI } from '@langchain/google-genai';

const GOOGLE_API_KEY = process.env.GOOGLE_API_KEY;

const llm = new ChatGoogleGenerativeAI({
  model: "gemini-2.0-flash",
  temperature: 0.7,
  apiKey: GOOGLE_API_KEY || "",
});

interface GenerateQuestionRequest {
  age_group: string;
  category?: string;
  difficulty?: string;
  question_type?: 'open' | 'mcq';
}

interface MCQResponse {
  question: string;
  options: string[];
  correct_answer: string;
}

const ageGroupPrompts = {
  "5-7": {
    description: "5-7 year olds (early elementary)",
    characteristics: "curious, imaginative, learning basic concepts, love stories and simple explanations",
    examples: "What would happen if animals could talk? Why do you think the sky is blue?"
  },
  "8-10": {
    description: "8-10 year olds (elementary)",
    characteristics: "developing logical thinking, interested in how things work, can handle more complex ideas",
    examples: "How would you design a robot to help at home? What do you think makes a good friend?"
  },
  "11-13": {
    description: "11-13 year olds (middle school)",
    characteristics: "abstract thinking developing, interested in social issues, can analyze problems",
    examples: "How would you solve bullying in schools? What impact does technology have on communication?"
  },
  "14-16": {
    description: "14-16 year olds (high school)",
    characteristics: "advanced reasoning, interested in real-world applications, can think critically about complex topics",
    examples: "How would you address climate change in your community? What skills will be most important for future careers?"
  },
  "17+": {
    description: "17+ year olds (young adults)",
    characteristics: "mature thinking, ready for complex philosophical and practical questions, preparing for adult responsibilities",
    examples: "How should society balance individual freedom with collective responsibility? What role should young people play in shaping the future?"
  }
};

async function generateMCQWithGemini(age_group: string, category: string, difficulty: string): Promise<MCQResponse> {
  if (!GOOGLE_API_KEY) {
    throw new Error('Gemini API key not configured');
  }

  const ageInfo = ageGroupPrompts[age_group as keyof typeof ageGroupPrompts];
  if (!ageInfo) {
    throw new Error(`Invalid age group: ${age_group}`);
  }

  const prompt = `Generate a multiple choice question for ${ageInfo.description}.

Target audience characteristics: ${ageInfo.characteristics}

Requirements:
- Category: ${category}
- Difficulty: ${difficulty}
- Age-appropriate language and concepts
- 4 options (A, B, C, D)
- Only ONE correct answer
- Engaging and educational
- Clear and unambiguous

Format your response as JSON:
{
  "question": "Your question here?",
  "options": ["Option A", "Option B", "Option C", "Option D"],
  "correct_answer": "Option A"
}

Generate ONE MCQ question only. Return only valid JSON, no additional text.`;

  try {
    const response = await llm.invoke(prompt);
    console.log('Gemini MCQ Response:', response);

    if (!response.content) {
      throw new Error('No content generated by Gemini');
    }

    let content = '';
    if (typeof response.content === 'string') {
      content = response.content;
    } else if (Array.isArray(response.content)) {
      const firstText = response.content
        .map((item: any) => {
          if (typeof item === 'string') return item;
          if (item && typeof item.text === 'string') return item.text;
          return null;
        })
        .find((text: string | null) => !!text);
      if (firstText) content = firstText;
    }

    if (!content) {
      throw new Error('No valid content generated by Gemini');
    }

    // Clean up the response to extract JSON
    const jsonMatch = content.match(/\{[\s\S]*\}/);
    if (!jsonMatch) {
      throw new Error('No JSON found in Gemini response');
    }

    const mcqData = JSON.parse(jsonMatch[0]);

    // Validate the response structure
    if (!mcqData.question || !mcqData.options || !mcqData.correct_answer) {
      throw new Error('Invalid MCQ structure from Gemini');
    }

    if (!Array.isArray(mcqData.options) || mcqData.options.length !== 4) {
      throw new Error('MCQ must have exactly 4 options');
    }

    if (!mcqData.options.includes(mcqData.correct_answer)) {
      throw new Error('Correct answer must be one of the options');
    }

    return mcqData;

  } catch (error) {
    console.error('Error calling Gemini API for MCQ:', error);
    throw error;
  }
}

// Get or create user performance for adaptive difficulty
async function getUserPerformance(userId: string, ageGroup: string) {
  let performance = await prisma.mindSparkPerformance.findUnique({
    where: {
      user_id_age_group: {
        user_id: userId,
        age_group: ageGroup
      }
    }
  });

  if (!performance) {
    performance = await prisma.mindSparkPerformance.create({
      data: {
        user_id: userId,
        age_group: ageGroup,
        current_difficulty: 'medium',
        consecutive_wrong: 0,
        consecutive_right: 0,
        total_questions: 0,
        total_correct: 0
      }
    });
  }

  return performance;
}

// Determine difficulty based on user performance
function getAdaptiveDifficulty(performance: any): string {
  // If user gets 3+ wrong in a row, reduce difficulty
  if (performance.consecutive_wrong >= 3) {
    if (performance.current_difficulty === 'hard') return 'medium';
    if (performance.current_difficulty === 'medium') return 'easy';
    return 'easy';
  }

  // If user gets 5+ right in a row, increase difficulty
  if (performance.consecutive_right >= 5) {
    if (performance.current_difficulty === 'easy') return 'medium';
    if (performance.current_difficulty === 'medium') return 'hard';
    return 'hard';
  }

  return performance.current_difficulty;
}

export async function POST(request: NextRequest) {
  try {
    // Get authenticated user
    const supabase = await createServerSupabaseClient();
    const { data: { user } } = await supabase.auth.getUser();
    
    if (!user) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Authentication required' 
        },
        { status: 401 }
      );
    }

    const body: GenerateQuestionRequest = await request.json();
    const { age_group, category = 'general', question_type = 'mcq' } = body;

    // Validate age group
    if (!ageGroupPrompts[age_group as keyof typeof ageGroupPrompts]) {
      return NextResponse.json(
        {
          success: false,
          error: 'Invalid age group. Must be one of: 5-7, 8-10, 11-13, 14-16, 17+'
        },
        { status: 400 }
      );
    }

    // Use default difficulty for now (will implement adaptive difficulty later)
    const adaptiveDifficulty = 'medium';

    console.log(`🤖 [MIND-SPARK-GENERATE] Generating ${question_type} question for age group: ${age_group}, category: ${category}, difficulty: ${adaptiveDifficulty}`);

    // Generate MCQ question using Gemini
    const mcqData = await generateMCQWithGemini(age_group, category, adaptiveDifficulty);

    console.log(`✨ [MIND-SPARK-GENERATE] Generated MCQ: ${mcqData.question}`);

    // Save to database
    const savedQuestion = await prisma.mindSparkQuestion.create({
      data: {
        question: mcqData.question,
        age_group,
        difficulty: adaptiveDifficulty,
        category,
        question_type,
        options: mcqData.options,
        correct_answer: mcqData.correct_answer,
        is_ai_generated: true
      }
    });

    console.log(`💾 [MIND-SPARK-GENERATE] Saved MCQ to database with ID: ${savedQuestion.id}`);

    return NextResponse.json({
      success: true,
      message: 'MCQ question generated and saved successfully',
      question: {
        id: savedQuestion.id,
        question: savedQuestion.question,
        age_group: savedQuestion.age_group,
        category: savedQuestion.category,
        difficulty: savedQuestion.difficulty,
        question_type: savedQuestion.question_type,
        options: savedQuestion.options,
        correct_answer: savedQuestion.correct_answer,
        is_ai_generated: savedQuestion.is_ai_generated,
        created_at: savedQuestion.created_at
      }
    });

  } catch (error) {
    console.error('❌ [MIND-SPARK-GENERATE] Error generating question:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to generate question',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
