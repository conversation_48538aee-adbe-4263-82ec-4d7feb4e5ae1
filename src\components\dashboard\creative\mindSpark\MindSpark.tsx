import React, { useState, useEffect, useCallback } from "react";
import { useAuth } from "@/hooks/useAuth";
import { toast } from "sonner";
import { AgeGroup, MindSparkProps, Question } from "./types";
import { determineAgeGroup } from "./questionUtils";
import QuestionHistory from "./QuestionHistory";
import ActiveQuestion from "./ActiveQuestion";
import MindSparkHeader from "./MindSparkHeader";
import MindSparkFooter from "./MindSparkFooter";
import { useMindSparkAPI } from "./hooks/useMindSparkAPI";

const MindSpark: React.FC<MindSparkProps> = ({ userAge = 10 }) => {
    const { user } = useAuth();
    const [currentQuestion, setCurrentQuestion] = useState<Question | null>(null);
    const [questions, setQuestions] = useState<Question[]>([]);

    const [showHistory, setShowHistory] = useState(false);
    const [isSubmitting, setIsSubmitting] = useState(false);
    const [selectedAgeGroup, setSelectedAgeGroup] = useState<AgeGroup>(() => {
        return determineAgeGroup(userAge);
    });

    const {
        isLoading,
        isGenerating,
        fetchQuestions,
        submitAnswer,
        generateQuestion,
        seedQuestions
    } = useMindSparkAPI();

    const loadQuestionsForAgeGroup = useCallback(async () => {
        const fetchedQuestions = await fetchQuestions(selectedAgeGroup, {
            limit: 20,
            include_answered: true
        });
        setQuestions(fetchedQuestions);

        if (fetchedQuestions.length > 0) {
            setCurrentQuestion(fetchedQuestions[0]);
        } else {
            setCurrentQuestion(null);
        }
    }, [selectedAgeGroup, fetchQuestions]);

    // Load questions when component mounts or age group changes
    useEffect(() => {
        loadQuestionsForAgeGroup();
    }, [loadQuestionsForAgeGroup]);

    const handleAgeGroupChange = (value: string) => {
        setSelectedAgeGroup(value as AgeGroup);
        setCurrentQuestion(null);
    };

    const handleSubmitAnswer = async (answer: string) => {
        if (!currentQuestion || !user) return;

        setIsSubmitting(true);
        const success = await submitAnswer(currentQuestion.id, answer);
        
        if (success) {
            // Mark question as answered
            const updatedQuestions = questions.map(q => 
                q.id === currentQuestion.id 
                    ? { ...q, is_answered: true, user_answer: { id: '', answer, answered_at: new Date().toISOString() } }
                    : q
            );
            setQuestions(updatedQuestions);
            setCurrentQuestion({ 
                ...currentQuestion, 
                is_answered: true, 
                user_answer: { id: '', answer, answered_at: new Date().toISOString() } 
            });
        }
        
        setIsSubmitting(false);
    };

    const handleNextQuestion = async () => {
        // Always generate a new question when user clicks "Next Question"
        const newQuestion = await generateQuestion(selectedAgeGroup);
        if (newQuestion) {
            const updatedQuestions = [newQuestion, ...questions];
            setQuestions(updatedQuestions);
            setCurrentQuestion(newQuestion);
            toast.success("New question generated!");
        } else {
            toast.error("Failed to generate new question. Please try again.");
        }
    };

    const handleGenerateQuestion = async () => {
        const newQuestion = await generateQuestion(selectedAgeGroup);
        if (newQuestion) {
            const updatedQuestions = [newQuestion, ...questions];
            setQuestions(updatedQuestions);
            setCurrentQuestion(newQuestion);
        }
    };

    // Initialize with seed questions if no questions exist
    useEffect(() => {
        const initializeQuestions = async () => {
            if (questions.length === 0 && !isLoading) {
                // Try to seed questions first
                await seedQuestions();
                // Then load questions
                await loadQuestionsForAgeGroup();
            }
        };

        if (user) {
            initializeQuestions();
        }
    }, [user, questions.length, isLoading, seedQuestions, loadQuestionsForAgeGroup]);

    return (
        <div className="bg-white rounded-xl shadow-sm p-6">
            <MindSparkHeader
                selectedAgeGroup={selectedAgeGroup}
                handleAgeGroupChange={handleAgeGroupChange}
                showHistory={showHistory}
                setShowHistory={setShowHistory}
                onGenerateQuestion={handleGenerateQuestion}
                isGenerating={isGenerating}
            />

            {isLoading && !currentQuestion && (
                <div className="text-center py-8">
                    <div className="inline-block h-8 w-8 animate-spin rounded-full border-4 border-solid border-spark-blue border-r-transparent align-[-0.125em] motion-reduce:animate-[spin_1.5s_linear_infinite]"></div>
                    <p className="mt-2 text-gray-600">Loading questions...</p>
                </div>
            )}

            {!user && (
                <div className="text-center py-8">
                    <p className="text-gray-600">Please sign in to access Mind Spark questions.</p>
                </div>
            )}

            {user && !isLoading && questions.length === 0 && (
                <div className="text-center py-8">
                    <p className="text-gray-600 mb-4">No questions available for this age group.</p>
                    <button
                        type="button"
                        onClick={handleGenerateQuestion}
                        disabled={isGenerating}
                        className="bg-spark-blue text-white px-4 py-2 rounded-lg hover:bg-spark-blue/90 disabled:opacity-50"
                    >
                        {isGenerating ? 'Generating...' : 'Generate First Question'}
                    </button>
                </div>
            )}

            {showHistory ? (
                <QuestionHistory
                    onBack={() => setShowHistory(false)}
                />
            ) : (
                <div>
                    {currentQuestion && user && (
                        <ActiveQuestion
                            currentQuestion={currentQuestion}
                            onSubmitAnswer={handleSubmitAnswer}
                            onNextQuestion={handleNextQuestion}
                            isSubmitting={isSubmitting}
                        />
                    )}
                </div>
            )}

            <MindSparkFooter />
        </div>
    );
};

export default MindSpark;
