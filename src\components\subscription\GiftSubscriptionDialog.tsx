'use client';
import React, { useState, useRef } from "react";
import { Dialog, DialogContent } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { SubscriptionPlan } from "@/components/subscription/SubscriptionPlans";
import { Calendar, X } from "lucide-react";
import { fredoka } from "@/lib/fonts";

interface GiftFormData {
    recipientEmail: string;
    senderName: string;
    giftMessage: string;
    deliveryDate: string;
    nameOnCard: string;
    cardNumber: string;
    expiryDate: string;
    cvv: string;
}

interface GiftSubscriptionDialogProps {
    open: boolean;
    onOpenChange: (open: boolean) => void;
    plan: SubscriptionPlan | null;
    onSubmit: (formData: GiftFormData) => void;
    isProcessing?: boolean;
}

const GiftSubscriptionDialog: React.FC<GiftSubscriptionDialogProps> = ({
    open,
    onOpenChange,
    plan,
    onSubmit,
    isProcessing = false,
}) => {
    const [formData, setFormData] = useState({
        recipientEmail: "",
        senderName: "",
        giftMessage: "",
        deliveryDate: "",
        nameOnCard: "",
        cardNumber: "",
        expiryDate: "",
        cvv: "",
    });

    const dateInputRef = useRef<HTMLInputElement>(null);

    const handleInputChange = (field: string, value: string) => {
        setFormData((prev) => ({
            ...prev,
            [field]: value,
        }));
    };

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        onSubmit(formData);
    };

    // Calculate actual price from displayed price
    const getActualPrice = (plan: SubscriptionPlan) => {
        const priceStr = plan.price.replace("$", "");
        const price = parseFloat(priceStr);

        if (plan.planId === "monthly-tier") {
            return price;
        } else if (plan.planId === "quarterly-tier") {
            return price * 3;
        } else if (plan.planId === "annual-tier") {
            return price * 12;
        }

        return price;
    };

    return (
        <Dialog open={open} onOpenChange={onOpenChange}>
            <DialogContent className="max-w-sm sm:max-w-[500px] max-h-[90vh] overflow-y-auto mx-4">
                {/* Close button */}
                <Button
                    onClick={() => onOpenChange(false)}
                    className="absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2"
                >
                    <X className="h-4 w-4" />
                </Button>

                <form onSubmit={handleSubmit} className="space-y-6">
                    {/* Plan info section */}
                    {plan && (
                        <div className="bg-gray-50 p-4 rounded-lg">
                            <div className="flex items-center justify-between">
                                <div>
                                    <h3 className="font-semibold text-gray-900">
                                        {plan.name}
                                    </h3>
                                    <p className="text-sm text-gray-600">
                                        {plan.description}
                                    </p>
                                </div>
                                <div className="text-right">
                                    <p className="text-lg font-bold text-gray-900">
                                        {plan.price}
                                        <span className="text-sm font-normal text-gray-600">
                                            {plan.priceSubtext}
                                        </span>
                                    </p>
                                    <p className="text-sm text-gray-600">
                                        Total: $
                                        {getActualPrice(plan).toFixed(2)}
                                    </p>
                                </div>
                            </div>
                        </div>
                    )}

                    {/* Recipient's Email */}
                    <div className="space-y-2">
                        <label className="text-sm font-medium text-gray-900">
                            Recipient&apos;s Email
                        </label>
                        <div className="relative">
                            <Input
                                type="email"
                                placeholder="<EMAIL>"
                                value={formData.recipientEmail}
                                onChange={(e) =>
                                    handleInputChange(
                                        "recipientEmail",
                                        e.target.value
                                    )
                                }
                                className="rounded-full border-2 border-emerald-500 focus:border-emerald-500 focus:ring-emerald-500"
                                required
                            />
                        </div>
                    </div>

                    {/* Your Name */}
                    <div className="space-y-2">
                        <label className="text-sm font-medium text-gray-900">
                            Your Name
                        </label>
                        <Input
                            type="text"
                            placeholder="Your Name"
                            value={formData.senderName}
                            onChange={(e) =>
                                handleInputChange("senderName", e.target.value)
                            }
                            className="rounded-lg"
                            required
                        />
                    </div>

                    {/* Gift Message */}
                    <div className="space-y-2">
                        <label className="text-sm font-medium text-gray-900">
                            Gift Message (Optional)
                        </label>
                        <textarea
                            placeholder="Enter a personal message..."
                            value={formData.giftMessage}
                            onChange={(e) =>
                                handleInputChange("giftMessage", e.target.value)
                            }
                            className="w-full p-3 border text-gray-900 border-gray-300 rounded-xl text-sm resize-none focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500"
                            rows={4}
                        />
                    </div>

                    {/* Delivery Date */}
                    <div className="space-y-2">
                        <label className="text-sm font-medium text-gray-900">
                            Delivery Date (Optional)
                        </label>
                        <div className="relative">
                            <Input
                                type="date"
                                placeholder="Pick a delivery date"
                                value={formData.deliveryDate}
                                onChange={(e) =>
                                    handleInputChange(
                                        "deliveryDate",
                                        e.target.value
                                    )
                                }
                                className="rounded-full !border-2 !border-emerald-500 !focus:border-emerald-500 !focus:ring-emerald-500 pr-10 [&::-webkit-calendar-picker-indicator]:hidden [&::-webkit-inner-spin-button]:hidden [&::-webkit-outer-spin-button]:hidden"
                                ref={dateInputRef}
                            />
                            <Calendar
                                className="absolute right-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400 cursor-pointer hover:text-gray-600 transition-colors"
                                onClick={() => {
                                    if (dateInputRef.current) {
                                        dateInputRef.current.showPicker?.();
                                    }
                                }}
                            />
                        </div>
                        <p className="text-xs text-gray-500">
                            The gift will be delivered to the recipient on this
                            date. If no date is selected, it will be sent
                            immediately.
                        </p>
                    </div>

                    {/* Payment Information */}
                    <div className="space-y-4">
                        <h3
                            className={`text-lg font-bold text-gray-900 ${fredoka.className}`}
                        >
                            Payment Information
                        </h3>

                        {/* Name on Card */}
                        <div className="space-y-2">
                            <label className="text-sm font-medium text-gray-900">
                                Name on Card
                            </label>
                            <Input
                                type="text"
                                placeholder="John Doe"
                                value={formData.nameOnCard}
                                onChange={(e) =>
                                    handleInputChange(
                                        "nameOnCard",
                                        e.target.value
                                    )
                                }
                                className="rounded-lg"
                                required
                            />
                        </div>

                        {/* Card Number */}
                        <div className="space-y-2">
                            <label className="text-sm font-medium text-gray-900">
                                Card Number
                            </label>
                            <Input
                                type="text"
                                placeholder="1234 5678 9012 3456"
                                value={formData.cardNumber}
                                onChange={(e) =>
                                    handleInputChange(
                                        "cardNumber",
                                        e.target.value
                                    )
                                }
                                className="rounded-lg"
                                maxLength={19}
                                required
                            />
                        </div>

                        {/* Expiry Date and CVV */}
                        <div className="grid grid-cols-2 gap-4">
                            <div className="space-y-2">
                                <label className="text-sm font-medium text-gray-900">
                                    Expiry Date
                                </label>
                                <Input
                                    type="text"
                                    placeholder="MM/YY"
                                    value={formData.expiryDate}
                                    onChange={(e) =>
                                        handleInputChange(
                                            "expiryDate",
                                            e.target.value
                                        )
                                    }
                                    className="rounded-lg"
                                    maxLength={5}
                                    required
                                />
                            </div>
                            <div className="space-y-2">
                                <label className="text-sm font-medium text-gray-900">
                                    CVV
                                </label>
                                <Input
                                    type="text"
                                    placeholder="123"
                                    value={formData.cvv}
                                    onChange={(e) =>
                                        handleInputChange("cvv", e.target.value)
                                    }
                                    className="rounded-lg"
                                    maxLength={4}
                                    required
                                />
                            </div>
                        </div>
                    </div>

                    {/* Submit Button */}
                    <div className="pt-4">
                        <Button
                            type="submit"
                            variant="default"
                            size="lg"
                            className="w-full !bg-emerald-600 !hover:bg-emerald-700 !text-white font-medium"
                            disabled={isProcessing}
                        >
                            {isProcessing
                                ? "Processing..."
                                : "Complete Gift Purchase"}
                        </Button>
                    </div>
                </form>
            </DialogContent>
        </Dialog>
    );
};

export default GiftSubscriptionDialog;
