import { NextResponse } from 'next/server'

export async function GET() {
  try {
    // Fetch stats from CMS
    const cmsResponse = await fetch('http://localhost:3001/api/sync/challenges', {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    })

    if (!cmsResponse.ok) {
      throw new Error('Failed to fetch CMS stats')
    }

    const cmsData = await cmsResponse.json()

    // Return formatted stats
    return NextResponse.json({
      success: true,
      stats: {
        totalChallenges: cmsData.stats?.cmsChallenge || 0,
        syncedChallenges: cmsData.stats?.mainAppCmsChallenge || 0,
        pendingChallenges: Math.max(0, (cmsData.stats?.cmsChallenge || 0) - (cmsData.stats?.mainAppCmsChallenge || 0)),
        totalUsers: 0, // Placeholder
        syncedUsers: 0, // Placeholder
        pendingUsers: 0, // Placeholder
      }
    })
  } catch (error) {
    console.error('Error fetching sync stats:', error)
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to fetch sync statistics',
        stats: {
          totalChallenges: 0,
          syncedChallenges: 0,
          pendingChallenges: 0,
          totalUsers: 0,
          syncedUsers: 0,
          pendingUsers: 0,
        }
      },
      { status: 500 }
    )
  }
}
