import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';

// Helper function to verify sync token
function verifySyncToken(req: NextRequest) {
  const authHeader = req.headers.get('authorization');
  const expectedToken = process.env.CMS_SYNC_TOKEN;
  
  if (!authHeader || !authHeader.startsWith('Bearer ') || !expectedToken) {
    return false;
  }
  
  const token = authHeader.substring(7);
  return token === expectedToken;
}

// GET /api/challenges/cms-count - Get count of CMS challenges in main app
export async function GET(request: NextRequest) {
  try {
    // Verify sync token
    if (!verifySyncToken(request)) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Count challenges that came from CMS (have cms_id)
    const count = await prisma.challenge.count({
      where: {
        cms_id: {
          not: null
        },
        is_active: true
      }
    });

    console.log(`📊 [MAIN-APP] CMS challenges count: ${count}`);

    return NextResponse.json({
      success: true,
      count
    });
  } catch (error) {
    console.error('❌ [MAIN-APP] Error counting CMS challenges:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to count CMS challenges' },
      { status: 500 }
    );
  }
}
