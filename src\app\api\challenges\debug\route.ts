import { NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';

// GET /api/challenges/debug - Debug challenges in database (no auth for debugging)
export async function GET() {
  try {
    console.log('🔍 [DEBUG] Checking challenges in database');

    // Count all challenges
    const totalCount = await prisma.challenge.count();
    
    // Count admin challenges
    const adminCount = await prisma.challenge.count({
      where: {
        created_by: 'admin'
      }
    });

    // Get all challenges
    const allChallenges = await prisma.challenge.findMany({
      orderBy: { created_at: 'desc' },
      take: 20
    });

    // Get admin challenges specifically
    const adminChallenges = await prisma.challenge.findMany({
      where: {
        created_by: 'admin'
      },
      orderBy: { created_at: 'desc' }
    });

    console.log(`📊 [DEBUG] Total challenges: ${totalCount}`);
    console.log(`📊 [DEBUG] Admin challenges: ${adminCount}`);

    return NextResponse.json({
      success: true,
      stats: {
        total: totalCount,
        admin: adminCount,
        other: totalCount - adminCount
      },
      allChallenges: allChallenges.map(c => ({
        id: c.id,
        title: c.title,
        created_by: c.created_by,
        created_at: c.created_at
      })),
      adminChallenges: adminChallenges.map(c => ({
        id: c.id,
        title: c.title,
        created_at: c.created_at
      }))
    });

  } catch (error) {
    console.error('❌ [DEBUG] Error checking challenges:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to check challenges',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
