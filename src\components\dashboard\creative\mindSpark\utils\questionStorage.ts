import { Question } from '../types';
// import { supabase } from '@/lib/supabase/client';
import { convertToJson } from './types';

// Save question history to user's content
export const saveQuestionHistory = async (userId: string, questions: Question[]): Promise<boolean> => {
  try {
    // Database operations disabled to prevent permission errors
    // Use localStorage for question history storage
    console.log('Database storage disabled - using localStorage only for question history');

    // Save to localStorage as primary storage
    const storageKey = `mindSparkQuestionHistory_${userId}`;
    const safeQuestions = convertToJson(questions);
    localStorage.setItem(storageKey, JSON.stringify(safeQuestions));

    return true;

    /* Commented out database operations
    const { data, error } = await supabase
      .from('user_content')
      .select('id')
      .eq('user_id', userId)
      .eq('type', 'mindspark_history')
      .single();

    if (error && error.code !== 'PGRST116') {
      console.error('Error checking for existing history:', error);
      return false;
    }

    // Convert questions to JSON-safe format
    const safeQuestions = convertToJson(questions);

    // Update or insert logic
    if (data) {
      // Update existing record
      const { error: updateError } = await supabase
        .from('user_content')
        .update({ content_metadata: { questions: safeQuestions } })
        .eq('id', data.id);

      if (updateError) {
        console.error('Error updating history:', updateError);
        return false;
      }
    } else {
      // Insert new record
      const { error: insertError } = await supabase
        .from('user_content')
        .insert({
          user_id: userId,
          type: 'mindspark_history',
          title: 'Mind Spark History',
          content_metadata: { questions: safeQuestions }
        });

      if (insertError) {
        console.error('Error inserting history:', insertError);
        return false;
      }
    }

    return true;
    */
  } catch (error) {
    console.error('Error saving question history:', error);
    return false;
  }
};

// Fetch question history for a user
export const fetchQuestionHistory = async (userId?: string): Promise<Question[]> => {
  try {
    // Try to load from localStorage first
    if (userId) {
      const storageKey = `mindSparkQuestionHistory_${userId}`;
      const stored = localStorage.getItem(storageKey);

      if (stored) {
        const questions = JSON.parse(stored) as Question[];
        console.log('Loaded question history from localStorage:', questions.length, 'questions');
        return questions;
      }
    }

    // If no localStorage data, return empty array
    return [];
  } catch (error) {
    console.error('Error in fetchQuestionHistory:', error);
    return [];
  }
};
