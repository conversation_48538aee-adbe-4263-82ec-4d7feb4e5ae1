import { NextRequest, NextResponse } from 'next/server';
import { createServerSupabaseClient } from '@/lib/supabase/server';
import { prisma } from '@/lib/prisma';
import Stripe from 'stripe';

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY!, {
  apiVersion: '2024-12-18.acacia',
});

function getPlanDisplayName(planId: string): string {
  switch (planId) {
    case 'monthly-tier':
      return 'Monthly Plan';
    case 'quarterly-tier':
      return 'Quarterly Plan';
    case 'annual-tier':
      return 'Annual Plan';
    default:
      return 'Unknown Plan';
  }
}

export async function POST(request: NextRequest) {
  try {
    const { planId, email, customerName, userId, promoCode, setupIntentId } = await request.json();

    if (!planId || !email || !customerName || !userId || !setupIntentId) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      );
    }

    // Verify user authentication
    const supabase = await createServerSupabaseClient();
    const { data: { user } } = await supabase.auth.getUser();

    if (!user || user.id !== userId) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Get user profile
    const profile = await prisma.profile.findUnique({
      where: { id: userId }
    });

    if (!profile) {
      return NextResponse.json(
        { error: 'User profile not found' },
        { status: 404 }
      );
    }

    // Validate promo code
    if (promoCode !== 'ALPHA100') {
      return NextResponse.json(
        { error: 'Invalid promo code' },
        { status: 400 }
      );
    }

    // Verify the setup intent was successful
    const setupIntent = await stripe.setupIntents.retrieve(setupIntentId);
    
    if (setupIntent.status !== 'succeeded') {
      return NextResponse.json(
        { error: 'Payment method setup failed' },
        { status: 400 }
      );
    }

    // Get the payment method from the setup intent
    const paymentMethodId = setupIntent.payment_method as string;

    // For ALPHA100, we'll just activate the subscription without creating a Stripe subscription
    // since it's 100% free. We'll store the payment method for future use.

    // Calculate subscription period
    const subscriptionPeriod = planId.includes('monthly') ? 30 :
                              planId.includes('quarterly') ? 90 : 365;

    // Update user profile with subscription details
    await prisma.profile.update({
      where: { id: userId },
      data: {
        subscription_status: 'active',
        plan_id: planId,
        plan_name: getPlanDisplayName(planId),
        billing_cycle: planId.includes('monthly') ? 'monthly' :
                      planId.includes('quarterly') ? 'quarterly' : 'annual',
        subscription_start: new Date(),
        subscription_end: new Date(Date.now() + subscriptionPeriod * 24 * 60 * 60 * 1000),
        trial_used: true, // Mark trial as used since they got free subscription
        updated_at: new Date()
      }
    });

    // Create a payment record for tracking
    await prisma.payment.create({
      data: {
        profile_id: userId,
        stripe_payment_id: `promo_${setupIntentId}`,
        amount: 0,
        currency: 'usd',
        status: 'succeeded',
        plan_id: planId,
        plan_name: getPlanDisplayName(planId),
        payment_date: new Date(),
        created_at: new Date()
      }
    });

    return NextResponse.json({
      success: true,
      message: 'Subscription activated with promo code!',
      planActivated: planId,
    });

  } catch (error) {
    console.error('Error activating promo subscription:', error);
    return NextResponse.json(
      { error: 'Failed to activate subscription' },
      { status: 500 }
    );
  }
}

function getPriceId(planId: string): string {
  // You'll need to replace these with your actual Stripe price IDs
  switch (planId) {
    case 'monthly-tier':
      return process.env.STRIPE_MONTHLY_PRICE_ID || 'price_monthly';
    case 'quarterly-tier':
      return process.env.STRIPE_QUARTERLY_PRICE_ID || 'price_quarterly';
    case 'annual-tier':
      return process.env.STRIPE_ANNUAL_PRICE_ID || 'price_annual';
    default:
      throw new Error(`Unknown plan ID: ${planId}`);
  }
}

function getOriginalAmount(planId: string): number {
  switch (planId) {
    case 'monthly-tier':
      return 1499; // $14.99 in cents
    case 'quarterly-tier':
      return 3597; // $35.97 in cents
    case 'annual-tier':
      return 11999; // $119.99 in cents
    default:
      return 0;
  }
}
