'use client';

import { useState } from 'react';
import { CMSChallengeGrid } from '@/components/cms/CMSChallengeGrid';
import { CMSStoryGrid } from '@/components/cms/CMSStoryGrid';
import { CMSEducationalGrid } from '@/components/cms/CMSEducationalGrid';
import { useSubscriptionStatus } from '@/hooks/useSubscriptionStatus';
import { BookOpen, Brush, Music, Gamepad2, Palette, Video, Code, Brain, GraduationCap } from 'lucide-react';

type ActiveTab = 'challenges' | 'stories' | 'learning' | 'tools';

interface TabConfig {
  id: ActiveTab;
  title: string;
  description: string;
  icon: React.ReactNode;
  color: string;
}

const tabs: TabConfig[] = [
  {
    id: 'challenges',
    title: 'Creative Challenges',
    description: 'Fun challenges to spark your creativity',
    icon: <Brush className="w-5 h-5" />,
    color: 'from-pink-500 to-rose-500',
  },
  {
    id: 'stories',
    title: 'Story Builder',
    description: 'Create amazing stories with guided prompts',
    icon: <BookOpen className="w-5 h-5" />,
    color: 'from-blue-500 to-cyan-500',
  },
  {
    id: 'learning',
    title: 'Learning Hub',
    description: 'Educational resources and tutorials',
    icon: <GraduationCap className="w-5 h-5" />,
    color: 'from-indigo-500 to-purple-500',
  },
  {
    id: 'tools',
    title: 'Creative Tools',
    description: 'Digital tools for art, music, and more',
    icon: <Palette className="w-5 h-5" />,
    color: 'from-green-500 to-emerald-500',
  },
];

const creativeTools = [
  {
    id: 'art-studio',
    title: 'Art Studio',
    description: 'Digital drawing and painting tools',
    icon: <Brush className="w-8 h-8" />,
    color: 'from-green-500 to-emerald-500',
    available: false,
    locked: true,
  },
  {
    id: 'music-maker',
    title: 'Music Maker',
    description: 'Compose and create your own music',
    icon: <Music className="w-8 h-8" />,
    color: 'from-yellow-500 to-orange-500',
    available: false,
    locked: true,
  },
  {
    id: 'video-creator',
    title: 'Video Creator',
    description: 'Make fun videos and animations',
    icon: <Video className="w-8 h-8" />,
    color: 'from-red-500 to-pink-500',
    available: false,
    locked: true,
  },
  {
    id: 'game-builder',
    title: 'Game Builder',
    description: 'Create simple games and interactive experiences',
    icon: <Gamepad2 className="w-8 h-8" />,
    color: 'from-purple-500 to-indigo-500',
    available: false,
    locked: true,
  },
  {
    id: 'code-playground',
    title: 'Code Playground',
    description: 'Learn programming with fun projects',
    icon: <Code className="w-8 h-8" />,
    color: 'from-blue-500 to-cyan-500',
    available: false,
    locked: true,
  },
  {
    id: 'mind-spark',
    title: 'Mind Spark',
    description: 'Answer thought-provoking questions',
    icon: <Brain className="w-8 h-8" />,
    color: 'from-purple-500 to-pink-500',
    available: true,
    locked: false,
    path: '/dashboard/creative/mindspark',
  },
];

export function CMSCreativeDashboard() {
  const [activeTab, setActiveTab] = useState<ActiveTab>('challenges');
  const [selectedCategory, setSelectedCategory] = useState<string>('');
  const [selectedAgeGroup, setSelectedAgeGroup] = useState<string>('');
  const { hasActiveSubscription } = useSubscriptionStatus();

  const categories = ['art', 'story', 'music', 'coding', 'video', 'game'];
  const ageGroups = ['6-8', '9-11', '12-14'];

  const handleToolClick = (tool: { available: boolean; path: string; locked: boolean; }) => {
    if (tool.available && tool.path) {
      window.location.href = tool.path;
    } else if (tool.locked && !hasActiveSubscription) {
      alert('This tool requires a premium subscription!');
    } else {
      alert('This tool is coming soon!');
    }
  };

  const renderContent = () => {
    switch (activeTab) {
      case 'challenges':
        return (
          <div>
            {/* Filters */}
            <div className="mb-6 flex flex-wrap gap-4">
              <select
                title="Select Category"
                value={selectedCategory}
                onChange={(e) => setSelectedCategory(e.target.value)}
                className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
                <option value="">All Categories</option>
                {categories.map((cat) => (
                  <option key={cat} value={cat}>
                    {cat.charAt(0).toUpperCase() + cat.slice(1)}
                  </option>
                ))}
              </select>
              
              <select
                aria-label="Select Age Group"
                title="Select Age Group"
                value={selectedAgeGroup}
                onChange={(e) => setSelectedAgeGroup(e.target.value)}
                className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
                <option value="">All Ages</option>
                {ageGroups.map((age) => (
                  <option key={age} value={age}>
                    {age} years
                  </option>
                ))}
              </select>
              
              <button
                onClick={() => {
                  setSelectedCategory('');
                  setSelectedAgeGroup('');
                }}
                className="px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors"
              >
                Clear Filters
              </button>
            </div>
            

            
            {/* All Challenges */}
            <div>
              <h3 className="text-xl font-bold mb-4">All Challenges</h3>
              <CMSChallengeGrid 
                category={selectedCategory || undefined}
                ageGroup={selectedAgeGroup || undefined}
              />
            </div>
          </div>
        );
        
      case 'stories':
        return (
          <div>
            <div className="mb-6">
              <h3 className="text-xl font-bold mb-4">📚 Story Templates</h3>
              <p className="text-gray-600 mb-4">
                Choose from our collection of story starters and create your own amazing tales!
              </p>
            </div>
            <CMSStoryGrid />
          </div>
        );
        
      case 'learning':
        return (
          <div>
            <div className="mb-6">
              <h3 className="text-xl font-bold mb-4">📖 Learning Resources</h3>
              <p className="text-gray-600 mb-4">
                Discover tutorials, guides, and educational content to enhance your creative skills.
              </p>
            </div>
            <CMSEducationalGrid />
          </div>
        );
        
      case 'tools':
        return (
          <div>
            <div className="mb-6">
              <h3 className="text-xl font-bold mb-4">🛠️ Creative Tools</h3>
              <p className="text-gray-600 mb-4">
                Digital tools to bring your creative ideas to life.
              </p>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {creativeTools.map((tool) => (
                <div
                  key={tool.id}
                  onClick={() => handleToolClick({
                    available: tool.available,
                    path: tool.path ?? '',
                    locked: tool.locked
                  })}
                  className={`bg-white rounded-xl p-6 shadow-lg border border-gray-200 cursor-pointer hover:shadow-xl transition-all duration-300 ${
                    tool.locked && !hasActiveSubscription ? 'opacity-75' : ''
                  }`}
                >
                  <div className="flex items-center gap-4 mb-4">
                    <div className={`w-12 h-12 bg-gradient-to-r ${tool.color} rounded-lg flex items-center justify-center text-white`}>
                      {tool.icon}
                    </div>
                    <div className="flex-1">
                      <h3 className="font-bold text-lg text-gray-800">{tool.title}</h3>
                      {tool.locked && !hasActiveSubscription && (
                        <span className="text-xs bg-yellow-100 text-yellow-800 px-2 py-1 rounded">
                          🔒 Premium
                        </span>
                      )}
                      {!tool.available && (
                        <span className="text-xs bg-gray-100 text-gray-600 px-2 py-1 rounded">
                          Coming Soon
                        </span>
                      )}
                    </div>
                  </div>
                  <p className="text-gray-600 text-sm">{tool.description}</p>
                </div>
              ))}
            </div>
          </div>
        );
        
      default:
        return null;
    }
  };

  return (
    <div className="max-w-7xl mx-auto p-6">
      {/* Header */}
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-800 mb-2">Creative Dashboard</h1>
        <p className="text-gray-600">
          Explore challenges, stories, and tools to unleash your creativity!
        </p>
      </div>

      {/* Tab Navigation */}
      <div className="mb-8">
        <div className="flex flex-wrap gap-2 border-b border-gray-200">
          {tabs.map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id)}
              className={`flex items-center gap-2 px-6 py-3 font-medium rounded-t-lg transition-colors ${
                activeTab === tab.id
                  ? 'bg-blue-50 text-blue-600 border-b-2 border-blue-600'
                  : 'text-gray-600 hover:text-gray-800 hover:bg-gray-50'
              }`}
            >
              {tab.icon}
              <span>{tab.title}</span>
            </button>
          ))}
        </div>
      </div>

      {/* Content */}
      <div className="min-h-[400px]">
        {renderContent()}
      </div>
    </div>
  );
}
