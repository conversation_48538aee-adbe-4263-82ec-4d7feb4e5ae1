import React from "react";
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
} from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";
import { AgeGroup } from "./types";

interface MindSparkHeaderProps {
    selectedAgeGroup: AgeGroup;
    handleAgeGroupChange: (value: string) => void;
    showHistory: boolean;
    setShowHistory: (show: boolean) => void;
    onGenerateQuestion?: () => void;
    isGenerating?: boolean;
}

const MindSparkHeader: React.FC<MindSparkHeaderProps> = ({
    selectedAgeGroup,
    handleAgeGroupChange,
    showHistory,
    setShowHistory,
    onGenerateQuestion,
    isGenerating = false,
}) => {
    return (
        <div className="flex justify-between items-center mb-6">
            <h2 className="text-2xl font-bold">Mind Spark</h2>
            <div className="flex items-center gap-4">
                <div className="flex items-center gap-2">
                    <Label className="text-sm text-gray-600">Age Group:</Label>
                    <Select
                        value={selectedAgeGroup}
                        onValueChange={handleAgeGroupChange}
                    >
                        <SelectTrigger className="w-24">
                            <SelectValue placeholder="Age" />
                        </SelectTrigger>
                        <SelectContent>
                            <SelectItem value="5-7">5-7</SelectItem>
                            <SelectItem value="8-10">8-10</SelectItem>
                            <SelectItem value="11-13">11-13</SelectItem>
                            <SelectItem value="14-16">14-16</SelectItem>
                            <SelectItem value="17+">17+</SelectItem>
                        </SelectContent>
                    </Select>
                </div>

                {onGenerateQuestion && (
                    <Button
                        onClick={onGenerateQuestion}
                        disabled={isGenerating}
                        variant="outline"
                        size="sm"
                        className="bg-gradient-to-r from-spark-blue to-spark-purple text-white border-0 hover:opacity-90"
                    >
                        {isGenerating ? (
                            <>
                                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                                Generating...
                            </>
                        ) : (
                            <>
                                🤖 Generate Question using AI
                            </>
                        )}
                    </Button>
                )}

                <div className="flex items-center gap-2">
                    <Label
                        htmlFor="history-toggle"
                        className="text-sm text-gray-600"
                    >
                        Show History
                    </Label>
                    <Switch
                        id="history-toggle"
                        checked={showHistory}
                        onCheckedChange={setShowHistory}
                    />
                </div>
            </div>
        </div>
    );
};

export default MindSparkHeader;
