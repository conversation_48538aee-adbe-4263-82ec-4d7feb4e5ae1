import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { createServerSupabaseClient } from '@/lib/supabase/server';

// Update user performance for adaptive difficulty
async function updateUserPerformance(userId: string, ageGroup: string, isCorrect: boolean) {
  // Get or create performance record
  let performance = await prisma.mindSparkPerformance.findUnique({
    where: {
      user_id_age_group: {
        user_id: userId,
        age_group: ageGroup
      }
    }
  });

  if (!performance) {
    performance = await prisma.mindSparkPerformance.create({
      data: {
        user_id: userId,
        age_group: ageGroup,
        current_difficulty: 'medium',
        consecutive_wrong: 0,
        consecutive_right: 0,
        total_questions: 0,
        total_correct: 0
      }
    });
  }

  // Update performance based on answer
  const updateData: any = {
    total_questions: performance.total_questions + 1,
    last_updated: new Date()
  };

  if (isCorrect) {
    updateData.total_correct = performance.total_correct + 1;
    updateData.consecutive_right = performance.consecutive_right + 1;
    updateData.consecutive_wrong = 0; // Reset wrong streak
  } else {
    updateData.consecutive_wrong = performance.consecutive_wrong + 1;
    updateData.consecutive_right = 0; // Reset right streak
  }

  // Update difficulty based on performance
  if (updateData.consecutive_wrong >= 3) {
    if (performance.current_difficulty === 'hard') updateData.current_difficulty = 'medium';
    else if (performance.current_difficulty === 'medium') updateData.current_difficulty = 'easy';
  } else if (updateData.consecutive_right >= 5) {
    if (performance.current_difficulty === 'easy') updateData.current_difficulty = 'medium';
    else if (performance.current_difficulty === 'medium') updateData.current_difficulty = 'hard';
  }

  // Save updated performance
  await prisma.mindSparkPerformance.update({
    where: {
      user_id_age_group: {
        user_id: userId,
        age_group: ageGroup
      }
    },
    data: updateData
  });

  console.log(`📊 [PERFORMANCE] Updated user ${userId} performance: ${isCorrect ? 'CORRECT' : 'WRONG'} - Consecutive: ${isCorrect ? updateData.consecutive_right : updateData.consecutive_wrong}`);
}

// GET /api/mind-spark/questions - Get questions by age group
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const age_group = searchParams.get('age_group');
    const category = searchParams.get('category');
    const limit = parseInt(searchParams.get('limit') || '10');
    const include_answered = searchParams.get('include_answered') === 'true';

    // Get authenticated user
    const supabase = await createServerSupabaseClient();
    const { data: { user } } = await supabase.auth.getUser();
    
    if (!user) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Authentication required' 
        },
        { status: 401 }
      );
    }

    // Build where clause
    const whereClause: any = {};
    
    if (age_group) {
      whereClause.age_group = age_group;
    }
    
    if (category) {
      whereClause.category = category;
    }

    // Get questions with user's answers
    const questions = await prisma.mindSparkQuestion.findMany({
      where: whereClause,
      include: {
        answers: {
          where: {
            user_id: user.id
          },
          select: {
            id: true,
            answer: true,
            answered_at: true
          }
        }
      },
      orderBy: [
        { is_ai_generated: 'asc' }, // Hardcoded questions first
        { created_at: 'desc' }
      ],
      take: limit
    });

    // Filter out answered questions if requested
    const filteredQuestions = include_answered 
      ? questions 
      : questions.filter(q => q.answers.length === 0);

    // Transform the response
    const transformedQuestions = filteredQuestions.map(question => ({
      id: question.id,
      question: question.question,
      age_group: question.age_group,
      difficulty: question.difficulty,
      category: question.category,
      is_ai_generated: question.is_ai_generated,
      created_at: question.created_at,
      is_answered: question.answers.length > 0,
      user_answer: question.answers[0] || null
    }));

    return NextResponse.json({
      success: true,
      questions: transformedQuestions,
      total: transformedQuestions.length
    });

  } catch (error) {
    console.error('❌ [MIND-SPARK-QUESTIONS] Error fetching questions:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to fetch questions',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

// POST /api/mind-spark/questions - Submit an answer to a question
export async function POST(request: NextRequest) {
  try {
    // Get authenticated user
    const supabase = await createServerSupabaseClient();
    const { data: { user } } = await supabase.auth.getUser();
    
    if (!user) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Authentication required' 
        },
        { status: 401 }
      );
    }

    const { question_id, answer } = await request.json();

    // Validate input
    if (!question_id || !answer || answer.trim().length === 0) {
      return NextResponse.json(
        {
          success: false,
          error: 'Question ID and answer are required'
        },
        { status: 400 }
      );
    }

    // Check if question exists
    const question = await prisma.mindSparkQuestion.findUnique({
      where: { id: question_id }
    });

    if (!question) {
      return NextResponse.json(
        {
          success: false,
          error: 'Question not found'
        },
        { status: 404 }
      );
    }

    // Check if user has already answered this question
    const existingAnswer = await prisma.mindSparkAnswer.findUnique({
      where: {
        user_id_question_id: {
          user_id: user.id,
          question_id: question_id
        }
      }
    });

    if (existingAnswer) {
      return NextResponse.json(
        {
          success: false,
          error: 'You have already answered this question'
        },
        { status: 409 }
      );
    }

    // For MCQ questions, check if answer is correct
    let isCorrect: boolean | null = null;
    if (question.question_type === 'mcq' && question.correct_answer) {
      isCorrect = answer.trim() === question.correct_answer;
    }

    // Save the answer
    const savedAnswer = await prisma.mindSparkAnswer.create({
      data: {
        user_id: user.id,
        question_id: question_id,
        answer: answer.trim(),
        is_correct: isCorrect
      },
      include: {
        question: {
          select: {
            question: true,
            age_group: true,
            category: true,
            difficulty: true,
            question_type: true,
            options: true,
            correct_answer: true
          }
        }
      }
    });

    // Update user performance for MCQ questions
    if (question.question_type === 'mcq' && isCorrect !== null) {
      await updateUserPerformance(user.id, question.age_group, isCorrect);
    }

    console.log(`💾 [MIND-SPARK-ANSWER] User ${user.id} answered question ${question_id}`);

    return NextResponse.json({
      success: true,
      message: 'Answer saved successfully',
      answer: {
        id: savedAnswer.id,
        answer: savedAnswer.answer,
        is_correct: savedAnswer.is_correct,
        answered_at: savedAnswer.answered_at,
        question: savedAnswer.question
      }
    });

  } catch (error) {
    console.error('❌ [MIND-SPARK-ANSWER] Error saving answer:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to save answer',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
