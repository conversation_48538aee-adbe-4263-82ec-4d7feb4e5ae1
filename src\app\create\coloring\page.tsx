'use client';
// src/app/create/coloring/page.tsx
import React, { useState, useRef, useEffect } from 'react';
import Link from 'next/link';
import { ArrowLeft, Palette, HelpCircle, Save, RefreshCw, Download as DownloadIcon, Upload } from 'lucide-react';
import { Button } from '@/components/ui/button';
import SafeModeIndicator from '@/components/shared/SafeModeIndicator';
import LearningModeToggle from '@/components/shared/LearningModeToggle';
import CreativeToolHelpDialog from '@/components/shared/CreativeToolHelpDialog';
import { Card, CardContent } from '@/components/ui/card';
import ColoringToolControls from '../../../components/coloring/ColoringToolControls';
import ColoringCanvas, { ColoringCanvasHandle, ToolType } from '../../../components/coloring/ColoringCanvas';
import AICreatorPanel from '../../../components/coloring/AICreatorPanel';
import { saveUserContent } from '@/utils/contentStorage';
import { toast } from 'sonner';

const coloringTips = [
  {
    key: 'fine-motor',
    label: 'Fine Motor Skills',
    content: (
      <>
        <div className="font-semibold text-lg mb-2">Developing Fine Motor Skills</div>
        <div className="mb-2">Coloring helps develop hand strength and control, which are essential for writing and other daily activities.</div>
        <ul className="list-disc pl-6 space-y-1">
          <li>Try to stay within the lines to improve precision</li>
          <li>Use different brush sizes to practice control</li>
          <li>Take breaks if your hand gets tired</li>
        </ul>
      </>
    )
  },
  {
    key: 'color-theory',
    label: 'Color Theory',
    content: (
      <>
        <div className="font-semibold text-lg mb-2">Exploring Color Theory</div>
        <div className="mb-2">Learn how colors work together to make your art pop! Try these tips:</div>
        <ul className="list-disc pl-6 space-y-1">
          <li>Experiment with complementary colors (opposites on the color wheel)</li>
          <li>Use warm and cool colors to set the mood</li>
          <li>Mix colors to create new shades</li>
        </ul>
      </>
    )
  },
  {
    key: 'creativity',
    label: 'Creativity',
    content: (
      <>
        <div className="font-semibold text-lg mb-2">Boosting Creativity</div>
        <div className="mb-2">Coloring is a great way to express yourself! Try these creative ideas:</div>
        <ul className="list-disc pl-6 space-y-1">
          <li>Use unexpected colors for familiar objects</li>
          <li>Add your own patterns or doodles</li>
          <li>Tell a story with your coloring page</li>
        </ul>
      </>
    )
  }
];

const aiSuggestions = [
  'Cute puppy dog',
  'Majestic lion',
  'Beautiful butterfly',
  'Racing car',
  'Flower garden',
  'Superhero character',
];

export default function ColoringPage() {
  const [learningMode, setLearningMode] = useState(true);
  const [helpDialogOpen, setHelpDialogOpen] = useState(false);
  const [selectedTip, setSelectedTip] = useState('fine-motor');
  // Drawing tool state
  const [tool, setTool] = useState<ToolType>('brush');
  const [color, setColor] = useState('#000000');
  const [brushSize, setBrushSize] = useState(10);
  const canvasRef = useRef<ColoringCanvasHandle>(null);
  const [activeTab, setActiveTab] = useState<'ai' | 'premade'>('ai');
  const [aiPrompt, setAiPrompt] = useState('');
  const [suggestions, setSuggestions] = useState(aiSuggestions);
  const [generating, setGenerating] = useState(false);
  const [enhancePrompt, setEnhancePrompt] = useState('');
  const [enhancing, setEnhancing] = useState(false);

  // Tool handlers
  const handleZoomIn = () => {
    canvasRef.current?.zoomIn();
  };

  const handleZoomOut = () => {
    canvasRef.current?.zoomOut();
  };

  const handleReset = () => canvasRef.current?.resetView();
  const handleDownload = () => canvasRef.current?.download();

  // Wrapper function to handle tool type conversion
  const handleToolChange = (newTool: string) => {
    const validTool = newTool as ToolType;
    setTool(validTool);
    canvasRef.current?.setTool(validTool);
  };

  // Pass tool/color/size to canvas
  useEffect(() => {
    canvasRef.current?.setTool(tool);
  }, [tool]);
  useEffect(() => {
    canvasRef.current?.setColor(color);
  }, [color]);
  useEffect(() => {
    canvasRef.current?.setBrushSize(brushSize);
  }, [brushSize]);

  // Handler for upload
  const handleUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file && canvasRef.current) {
      const reader = new FileReader();
      reader.onload = () => {
        if (typeof reader.result === 'string') {
          canvasRef.current?.loadImage(reader.result);
        }
      };
      reader.readAsDataURL(file);
    }
  };

  // Handler for AI suggestion click
  const handleSuggestionClick = (text: string) => setAiPrompt(text);
  // Handler for refresh
  const handleRefresh = () => setSuggestions(suggestions.slice().sort(() => Math.random() - 0.5));

  // Call backend to generate AI coloring page
  const handleCreate = async () => {
    console.log('🔥 CLIENT: handleCreate called with prompt:', aiPrompt);
    if (!aiPrompt.trim()) return;
    setGenerating(true);
    try {
      console.log('🔥 CLIENT: Making API call to /api/generate-coloring');
      const res = await fetch('/api/generate-coloring', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ prompt: aiPrompt }),
      });
      console.log('🔥 CLIENT: API response status:', res.status);
      const data = await res.json();
      console.log('🔥 CLIENT: API response data:', data);
      
      if (res.ok && data.image) {
        console.log('🔥 CLIENT: Success! Loading image to canvas:', data.image);
        console.log('🔥 CLIENT: canvasRef.current:', canvasRef.current);
        // Use the canvas loadImage method instead of direct DOM manipulation
        canvasRef.current?.loadImage(data.image);
        // Clear input prompt after generation
        setAiPrompt('');
      } else {
        console.log('🔥 CLIENT: Error generating image:', data.error);
        console.error('Failed to generate image:', data.error);
        alert('Failed to generate image. Please try again.');
      }
    } catch (err) {
      console.log('🔥 CLIENT: Exception in handleCreate:', err);
      console.error('Error generating image:', err);
      alert('Failed to generate image. Please try again.');
    } finally {
      console.log('🔥 CLIENT: Setting generating to false');
      setGenerating(false);
    }
  };

  // Enhance canvas with AI
  const handleEnhance = async () => {
    console.log('🔥 CLIENT: handleEnhance called');
    if (!canvasRef.current) return;

    // Always get the current canvas data
    const canvasData = canvasRef.current.getCanvasData();
    if (!canvasData) {
      alert('Please generate or draw line art first before enhancing.');
      return;
    }
    const userPrompt = enhancePrompt.trim();
    const isCustom = Boolean(userPrompt);
    const apiRoute = isCustom ? '/api/controlnet-enhance' : '/api/enhance-canvas';
    const bodyPayload: Record<string, unknown> = isCustom
      ? { imageData: canvasData, prompt: userPrompt }
      : { imageData: canvasData };

    setEnhancing(true);
    try {
      console.log(`🔥 CLIENT: Making API call to ${apiRoute}`, bodyPayload);
      const res = await fetch(apiRoute, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(bodyPayload),
      });
      console.log('🔥 CLIENT: Enhance API response status:', res.status);
      const data = await res.json();
      console.log('🔥 CLIENT: Enhance API response data:', data);
      
      if (res.ok && data.image) {
        canvasRef.current?.loadImage(data.image);
      } else {
        console.log('🔥 CLIENT: Error enhancing image:', data.error);
        console.error('Failed to enhance image:', data.error);
        alert('Failed to enhance image. Please try again.');
      }
    } catch (err) {
      console.log('🔥 CLIENT: Exception in handleEnhance:', err);
      console.error('Error enhancing image:', err);
      alert('Failed to enhance image. Please try again.');
    } finally {
      console.log('🔥 CLIENT: Setting enhancing to false');
      setEnhancing(false);
    }
  };

  // Function to generate prompt via API
  // const handleGeneratePrompt = async () => {
  //   try {
  //     const res = await fetch('/api/generate-prompt');
  //     const data = await res.json();
  //     if (res.ok && data.prompt) {
  //       setAiPrompt(data.prompt);
  //     } else {
  //       console.error('Prompt generation failed:', data.error);
  //       alert('Failed to generate prompt.');
  //     }
  //   } catch (err) {
  //     console.error('Error generating prompt:', err);
  //     alert('Failed to generate prompt.');
  //   }
  // };

  // Save to Portfolio handler
  const handleSaveToPortfolio = async () => {
    const imageData = canvasRef.current?.getCanvasData();
    if (!imageData) {
      toast.error('Please color something before saving!');
      return;
    }
    const success = await saveUserContent({
      type: 'art',
      title: 'My Coloring Page',
      content_metadata: {},
      preview_url: imageData,
    });
    if (success) {
      toast.success('Saved to portfolio!');
    } else {
      toast.error('Failed to save. Try again!');
    }
  };

  return (
    <div className="max-w-6xl mx-auto px-4 py-8 flex flex-col items-center">
      {/* Header */}
      <div className="w-full mb-8">
        {/* Row 1: Back to Dashboard */}
        <div>
        <Link href="/dashboard">
          <Button variant="ghost" className="gap-1 min-h-[44px] text-sm sm:text-base">
            <ArrowLeft className="h-4 w-4" />
            <span className="sm:hidden">Dashboard</span>
              <span className="hidden sm:inline ">Back to Dashboard</span>
          </Button>
        </Link>
        </div>
        {/* Row 2: Title + Safe Mode (left), Buttons (right) */}
        <div className="flex justify-between items-center mt-4">
          <div>
            <h1 className="text-xl sm:text-2xl lg:text-3xl font-bold flex items-center">
              <Palette className="h-5 w-5 sm:h-6 sm:w-6 lg:h-7 lg:w-7 text-spark-blue mr-2" />
              Coloring Pages
            </h1>
            <div className="mt-2">
              <SafeModeIndicator type="coloring" />
            </div>
          </div>
          <div className="flex items-center gap-2 sm:gap-3">
          <Button 
            variant="outline" 
            className="!border-[#00BFA5] !text-[#00BFA5] hover:!bg-littlespark-primary hover:!text-white rounded-full min-h-[44px] text-sm sm:text-base"
            onClick={() => setHelpDialogOpen(true)}
          >
            <HelpCircle className="h-4 w-4" />
            <span className="hidden sm:inline ml-2">How to Use</span>
          </Button>
          <LearningModeToggle 
            learningMode={learningMode}
            toggleLearningMode={() => setLearningMode((v) => !v)}
          />
        </div>
      </div>
      </div>
      {/* Tips Section */}
      {learningMode && (
        <Card className="mb-8 bg-blue-50 w-full">
          <CardContent>
            <div className="mb-4 w-full">
              <div className="flex items-center gap-2 mb-1">
                <span className="text-spark-blue text-2xl">🏅</span>
                <span className="font-bold text-lg">Creative Coloring Tips</span>
              </div>
              <span className="text-gray-600 text-sm">Choose a category to explore different coloring skills!</span>
            </div>
            <div className="flex gap-2 mb-4">
              {coloringTips.map((tip) => (
                <Button
                  key={tip.key}
                  variant="outline"
                  className={`rounded-full min-w-[160px] px-6 py-2 font-medium border-2 transition-colors duration-150
                    ${selectedTip === tip.key
                      ? '!border-[#00BFA5] !bg-[#00BFA5] !text-white'
                      : '!border-[#00BFA5] !text-[#00BFA5] !bg-white hover:!bg-[#00BFA5] hover:!text-white'}
                  `}
                  onClick={() => setSelectedTip(tip.key)}
                >
                  {tip.label}
                </Button>
              ))}
            </div>
            <div className="bg-white rounded-xl p-4 shadow">
              {coloringTips.find((tip) => tip.key === selectedTip)?.content}
            </div>
          </CardContent>
        </Card>
      )}
      {/* Main Drawing Area: Two-column layout */}
      <div className="w-full flex flex-col lg:flex-row gap-4 lg:gap-8 justify-center items-start mb-4 max-w-full">
        {/* Left: Canvas + All Actions Below */}
        <div className="flex-1 min-w-0 w-full">
          <div className="bg-white rounded-3xl shadow-lg p-4 lg:p-6 flex flex-col items-center">
            <div className="flex items-center justify-center w-full max-w-[720px] h-auto lg:h-[540px]" style={{minHeight: '400px'}}>
            <ColoringCanvas
              ref={canvasRef}
              width={700}
              height={500}
            />
          </div>
            {/* Enhancement prompt input and button - single row */}
            <div className="flex flex-col sm:flex-row items-center gap-2 w-full mt-4 lg:mt-6">
              <input
                type="text"
                value={enhancePrompt}
                onChange={e => setEnhancePrompt(e.target.value)}
                placeholder="Enhancement prompt (optional)"
                className="flex-1 rounded-full border border-gray-300 px-4 py-2 text-base outline-none min-w-0"
              />
              <Button
                className={`rounded-full px-4 lg:px-6 py-2 bg-gradient-to-r from-purple-500 to-pink-500 text-white font-semibold whitespace-nowrap text-sm lg:text-base ${enhancing ? 'opacity-60 cursor-not-allowed' : ''}`}
                onClick={handleEnhance}
                disabled={enhancing}
              >
                {enhancing ? 'Enhancing...' : '✨ AI Enhance'}
              </Button>
        </div>
            {/* Action Buttons Bar - single row */}
            <div className="w-full flex flex-wrap justify-center gap-2 lg:gap-4 mt-4 lg:mt-6">
              <Button className={`rounded-full px-4 lg:px-8 py-2 lg:py-3 flex items-center gap-2 text-sm lg:text-base font-semibold min-w-[140px] lg:min-w-[220px] border-2 !bg-[#00BFA5] !text-white !border-[#00BFA5] hover:!bg-[#00BFA5] hover:!text-white`} onClick={handleSaveToPortfolio}>
            <Save className="h-4 w-4 lg:h-5 lg:w-5" />
            <span className="hidden sm:inline">Save to Portfolio</span>
            <span className="sm:hidden">Save</span>
          </Button>
          <Button
                className="rounded-full px-4 lg:px-8 py-2 lg:py-3 flex items-center gap-2 border-2 !border-[#00BFA5] !text-[#00BFA5] !bg-white text-sm lg:text-base font-semibold min-w-[100px] lg:min-w-[180px] hover:!bg-[#00BFA5] hover:!text-white"
            onClick={() => canvasRef.current?.clear()}
          >
            <RefreshCw className="h-4 w-4 lg:h-5 lg:w-5" />
            <span className="hidden sm:inline">Clear</span>
            <span className="sm:hidden">Clear</span>
          </Button>
          <Button
                className="rounded-full px-4 lg:px-8 py-2 lg:py-3 flex items-center gap-2 border-2 !border-[#00BFA5] !text-[#00BFA5] !bg-white text-sm lg:text-base font-semibold min-w-[100px] lg:min-w-[180px] hover:!bg-[#00BFA5] hover:!text-white"
            onClick={() => canvasRef.current?.download()}
          >
            <DownloadIcon className="h-4 w-4 lg:h-5 lg:w-5" />
            <span className="hidden sm:inline">Download</span>
            <span className="sm:hidden">Download</span>
          </Button>
              <label className="rounded-full px-4 lg:px-8 py-2 lg:py-3 flex items-center gap-2 border-2 !border-[#00BFA5] !text-[#00BFA5] !bg-white text-sm lg:text-base font-semibold cursor-pointer min-w-[140px] lg:min-w-[220px] hover:!bg-[#00BFA5] hover:!text-white" style={{marginBottom:0}}>
            <Upload className="h-4 w-4 lg:h-5 lg:w-5" />
                <span className="hidden sm:inline">Upload Your Drawing</span>
                <span className="sm:hidden">Upload</span>
            <input type="file" accept="image/*" className="hidden" onChange={handleUpload} />
          </label>
        </div>
            {/* Toggle Buttons Row - AI/Premade */}
            <div className="w-full flex flex-col sm:flex-row justify-center gap-2 lg:gap-4 mt-4 lg:mt-6">
          <Button
                className={`rounded-full px-4 lg:px-8 py-3 lg:py-4 flex-1 text-base lg:text-lg font-semibold max-w-full lg:max-w-[500px] border-2 ${activeTab === 'ai' ? '!bg-[#00BFA5] !text-white !border-[#00BFA5]' : '!bg-white !text-[#00BFA5] !border-[#00BFA5] hover:!bg-[#00BFA5] hover:!text-white'}`}
            onClick={() => { setActiveTab('ai'); }}
          >
            <span className="hidden sm:inline">AI Coloring Page Creator</span>
            <span className="sm:hidden">AI Creator</span>
          </Button>
          <Button
                className={`rounded-full px-4 lg:px-8 py-3 lg:py-4 flex-1 text-base lg:text-lg font-semibold max-w-full lg:max-w-[500px] border-2 ${activeTab === 'premade' ? '!bg-[#00BFA5] !text-white !border-[#00BFA5]' : '!bg-white !text-[#00BFA5] !border-[#00BFA5] hover:!bg-[#00BFA5] hover:!text-white'}`}
            onClick={() => { setActiveTab('premade'); }}
          >
            <span className="hidden sm:inline">Premade Coloring Pages</span>
            <span className="sm:hidden">Premade Pages</span>
          </Button>
        </div>
            {/* AI Panel Component (only for AI tab) */}
        {activeTab === 'ai' && (
              <div className="w-full mt-4 lg:mt-8">
          <AICreatorPanel
            aiPrompt={aiPrompt}
            setAiPrompt={setAiPrompt}
            suggestions={suggestions}
            onSuggestionClick={handleSuggestionClick}
            onRefresh={handleRefresh}
            onCreate={handleCreate}
            generating={generating}
          />
              </div>
        )}
          </div>
        </div>
        {/* Right Sidebar: Only ColoringToolControls card, remove Choose a Coloring Page card */}
        <div className="w-full lg:w-[340px] flex flex-col gap-4 lg:gap-6 mt-4 lg:mt-0">
          <div className="bg-white rounded-3xl shadow-lg p-4">
            <ColoringToolControls
              tool={tool}
              setTool={handleToolChange}
              color={color}
              setColor={setColor}
              brushSize={brushSize}
              setBrushSize={setBrushSize}
              onZoomIn={handleZoomIn}
              onZoomOut={handleZoomOut}
              onReset={handleReset}
              onDownload={handleDownload}
              onUndo={() => canvasRef.current?.undo()}
              onRedo={() => canvasRef.current?.redo()}
        />
          </div>
        </div>
      </div>
      {/* Help Dialog */}
      <CreativeToolHelpDialog
        open={helpDialogOpen}
        onOpenChange={setHelpDialogOpen}
        title="Coloring Pages"
        description="Create and color beautiful pages with AI-powered tools!"
        steps={[
          {
            title: '1. Generate Line Art',
            content: 'Generate line art using AI below'
          },
          {
            title: '2. Color Manually',
            content: 'Color it manually with tools on the right'
          },
          {
            title: '3. AI Enhance',
            content: 'Use "AI Enhance" button to polish your artwork'
          }
        ]}
        aiMentorMessage="Let's have fun coloring! Follow these simple steps to create your masterpiece."
      />
    </div>
  );
} 