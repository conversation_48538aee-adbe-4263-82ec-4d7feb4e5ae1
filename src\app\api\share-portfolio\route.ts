import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { createServerSupabaseClient } from '@/lib/supabase/server';
import { Resend } from 'resend';

// POST /api/share-portfolio - Send portfolio via email to parents
export async function POST(request: NextRequest) {
  try {
    // Get authenticated user
    const supabase = await createServerSupabaseClient();
    const { data: { user } } = await supabase.auth.getUser();
    
    if (!user) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Authentication required' 
        },
        { status: 401 }
      );
    }

    const { parentEmail, message, includeStats, includeProjects } = await request.json();

    // Validate email
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!parentEmail || !emailRegex.test(parentEmail)) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Valid parent email is required' 
        },
        { status: 400 }
      );
    }

    // Get user profile
    const profile = await prisma.profile.findUnique({
      where: { id: user.id },
      select: {
        full_name: true,
        email: true
      }
    });

    if (!profile) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'User profile not found' 
        },
        { status: 404 }
      );
    }

    // Get user's content and stats
    let userContent: Record<string, unknown>[] = [];
    let contentStats = { stories: 0, artwork: 0, music: 0, total: 0 };

    if (includeProjects || includeStats) {
      userContent = await prisma.userContent.findMany({
        where: {
          user_id: user.id,
          type: {
            in: ['story', 'art', 'music']
          }
        },
        include: {
          challenge: {
            select: {
              title: true,
              type: true,
              difficulty: true
            }
          }
        },
        orderBy: {
          created_at: 'desc'
        },
        take: 10 // Limit to recent 10 projects for email
      });

      // Calculate stats
      contentStats = userContent.reduce((acc: { stories: number; artwork: number; music: number; total: number }, content) => {
        acc.total++;
        switch (content.type as string) {
          case 'story':
            acc.stories++;
            break;
          case 'art':
            acc.artwork++;
            break;
          case 'music':
            acc.music++;
            break;
        }
        return acc;
      }, { stories: 0, artwork: 0, music: 0, total: 0 });
    }

    // Generate share token for secure access
    const shareToken = Buffer.from(JSON.stringify({
      userId: user.id,
      timestamp: Date.now(),
      type: 'portfolio'
    })).toString('base64url');

    const portfolioUrl = `${process.env.NEXT_PUBLIC_SITE_URL}/shared/portfolio/${shareToken}`;

    // Create email content
    const childName = profile.full_name || 'Your child';
    const emailSubject = `${childName}'s Creative Portfolio from Little Spark`;
    
    const emailHtml = `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <title>${emailSubject}</title>
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px; }
          .header { background: linear-gradient(135deg, #0ea5e9 0%, #06b6d4 100%); color: white; padding: 30px; text-align: center; border-radius: 10px 10px 0 0; }
          .content { background: #f8fafc; padding: 30px; border-radius: 0 0 10px 10px; }
          .stats { background: white; padding: 20px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #06b6d4; }
          .project { background: white; padding: 15px; margin: 10px 0; border-radius: 8px; border: 1px solid #e2e8f0; }
          .button { display: inline-block; background: #06b6d4; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; margin: 20px 0; }
          .footer { text-align: center; margin-top: 30px; color: #64748b; font-size: 14px; }
        </style>
      </head>
      <body>
        <div class="header">
          <h1>🎨 ${childName}'s Creative Portfolio</h1>
          <p>Shared from Little Spark - Where Creativity Comes to Life!</p>
        </div>
        
        <div class="content">
          ${message ? `
            <div style="background: #fef3c7; padding: 15px; border-radius: 8px; margin-bottom: 20px; border-left: 4px solid #f59e0b;">
              <h3 style="margin: 0 0 10px 0; color: #92400e;">Personal Message:</h3>
              <p style="margin: 0; color: #92400e;">"${message}"</p>
            </div>
          ` : ''}
          
          <p>Hello! ${childName} has been creating amazing projects on Little Spark and wanted to share their creative journey with you.</p>
          
          ${includeStats ? `
            <div class="stats">
              <h3 style="margin: 0 0 15px 0; color: #0f172a;">📊 Creative Achievements</h3>
              <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(120px, 1fr)); gap: 15px;">
                <div style="text-align: center;">
                  <div style="font-size: 24px; font-weight: bold; color: #3b82f6;">${contentStats.stories}</div>
                  <div style="color: #64748b;">Stories</div>
                </div>
                <div style="text-align: center;">
                  <div style="font-size: 24px; font-weight: bold; color: #10b981;">${contentStats.artwork}</div>
                  <div style="color: #64748b;">Artwork</div>
                </div>
                <div style="text-align: center;">
                  <div style="font-size: 24px; font-weight: bold; color: #8b5cf6;">${contentStats.music}</div>
                  <div style="color: #64748b;">Music</div>
                </div>
                <div style="text-align: center;">
                  <div style="font-size: 24px; font-weight: bold; color: #f59e0b;">${contentStats.total}</div>
                  <div style="color: #64748b;">Total Projects</div>
                </div>
              </div>
            </div>
          ` : ''}
          
          ${includeProjects && userContent.length > 0 ? `
            <h3 style="color: #0f172a; margin: 25px 0 15px 0;">🎯 Recent Projects</h3>
            ${userContent.slice(0, 5).map(project => `
              <div class="project">
                <h4 style="margin: 0 0 8px 0; color: #1e293b;">${project.title}</h4>
                <div style="display: flex; align-items: center; gap: 10px; color: #64748b; font-size: 14px;">
                  <span style="background: #e2e8f0; padding: 2px 8px; border-radius: 12px;">${(project.type as string).charAt(0).toUpperCase() + (project.type as string).slice(1)}</span>
                  ${project.challenge ? `<span style="background: #fef3c7; color: #92400e; padding: 2px 8px; border-radius: 12px;">Challenge Project</span>` : ''}
                  <span>${new Date(project.created_at as string).toLocaleDateString()}</span>
                </div>
              </div>
            `).join('')}
            ${userContent.length > 5 ? `<p style="color: #64748b; font-style: italic;">...and ${userContent.length - 5} more projects!</p>` : ''}
          ` : ''}
          
          <div style="text-align: center; margin: 30px 0;">
            <a href="${portfolioUrl}" class="button">🔗 View Full Portfolio</a>
          </div>
          
          <div style="background: #ecfdf5; padding: 15px; border-radius: 8px; border-left: 4px solid #10b981;">
            <h4 style="margin: 0 0 10px 0; color: #065f46;">🌟 About Little Spark</h4>
            <p style="margin: 0; color: #065f46; font-size: 14px;">Little Spark is a creative platform where children can write stories, create artwork, compose music, and participate in fun challenges. It's designed to nurture creativity, imagination, and self-expression in a safe, engaging environment.</p>
          </div>
        </div>
        
        <div class="footer">
          <p>This portfolio link will remain active for 30 days.</p>
          <p>Sent with ❤️ from Little Spark | <a href="${process.env.NEXT_PUBLIC_SITE_URL}" style="color: #06b6d4;">Visit Little Spark</a></p>
        </div>
      </body>
      </html>
    `;

    // Send email using Resend
    const resend = new Resend(process.env.RESEND_API_KEY);
    await resend.emails.send({
      from: '"Little Spark" <<EMAIL>>',
      to: parentEmail,
      subject: emailSubject,
      html: emailHtml,
    });
    console.log(`Portfolio email sent to ${parentEmail} via Resend.`);

    // Log the share activity (optional)
    try {
      await prisma.userContent.create({
        data: {
          user_id: user.id,
          type: 'share',
          title: `Portfolio shared with ${parentEmail}`,
          content_metadata: {
            parentEmail,
            shareToken,
            message,
            sharedAt: new Date().toISOString(),
            contentCount: contentStats.total
          },
          preview_url: portfolioUrl
        }
      });
    } catch (logError) {
      console.error('Error logging share activity:', logError);
      // Don't fail the request if logging fails
    }

    return NextResponse.json({
      success: true,
      message: 'Portfolio shared successfully',
      portfolioUrl,
      sentTo: parentEmail
    });

  } catch (error) {
    console.error('Error sharing portfolio:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to send email. Please try again.' 
      },
      { status: 500 }
    );
  }
}
