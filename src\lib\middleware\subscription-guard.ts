import { NextRequest, NextResponse } from 'next/server';
import { createServerSupabaseClient } from '@/lib/supabase/server';
import { checkUserAccess } from '@/lib/subscription-utils';


/**
 * Middleware to check subscription access for protected API routes
 */
export async function withSubscriptionGuard(
  request: NextRequest,
  handler: (request: NextRequest) => Promise<NextResponse>
): Promise<NextResponse> {
  try {
    const supabase = await createServerSupabaseClient();
    const { data: { user } } = await supabase.auth.getUser();

    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check subscription access
    const accessResult = await checkUserAccess(user.id);

    if (!accessResult.hasAccess) {
      // No longer auto-activate trials - require payment method setup first
      console.log(`Access denied for user ${user.id}: ${accessResult.reason}`);
      return NextResponse.json({
        error: 'Subscription required',
        reason: accessResult.reason,
        hasAccess: false,
        requiresSubscription: true
      }, { status: 403 });
    }



    // If access is granted, proceed with the original handler
    return await handler(request);
    
  } catch (error) {
    console.error('Error in subscription guard:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

/**
 * Higher-order function to wrap API route handlers with subscription checking
 */
export function requireSubscription(
  handler: (request: NextRequest) => Promise<NextResponse>
) {
  return async (request: NextRequest): Promise<NextResponse> => {
    return withSubscriptionGuard(request, handler);
  };
}

/**
 * Check if a specific API route should be protected
 */
export function shouldProtectRoute(pathname: string): boolean {
  const protectedRoutes = [
    '/api/art/generate',
    '/api/story/generate',
    '/api/music/generate',
    '/api/user-content',
    '/api/challenges/mark-complete',
    '/api/challenges/validate-completion'
  ];
  
  return protectedRoutes.some(route => pathname.startsWith(route));
}
