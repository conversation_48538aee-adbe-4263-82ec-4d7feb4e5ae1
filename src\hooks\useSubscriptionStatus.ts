import { useState, useEffect } from 'react';
import { type AccessControlResult } from '@/lib/subscription-utils';
import { useAuth } from './useAuth';
import { useApiCache } from './useApiCache';

export const useSubscriptionStatus = () => {
  const { user } = useAuth();
  const [showRenewalBanner, setShowRenewalBanner] = useState(false);

  // Use cached API call with 2 minute cache time
  const {
    data: accessResult,
    isLoading,
    refetch: refreshSubscriptionStatus
  } = useApiCache<AccessControlResult>(
    user?.id ? `subscription-status-${user.id}` : '',
    async () => {
      if (!user?.id) throw new Error('No user ID');

      const response = await fetch(`/api/subscription/access-check?userId=${user.id}`);
      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        const errorMessage = errorData.details || errorData.error || 'Failed to fetch subscription status';
        throw new Error(errorMessage);
      }
      return response.json();
    },
    {
      cacheTime: 2 * 60 * 1000, // 2 minutes cache
      staleTime: 30 * 1000, // 30 seconds stale time
      refetchOnWindowFocus: false // Disable window focus refetch
    }
  );

  // Derived state from cached data
  const subscriptionStatus = accessResult?.subscriptionStatus || null;
  const hasActiveSubscription = accessResult?.hasAccess || false;

  // Update renewal banner based on access result
  useEffect(() => {
    if (accessResult?.daysUntilExpiry !== undefined &&
        accessResult.daysUntilExpiry <= 3 &&
        accessResult.daysUntilExpiry > 0) {
      setShowRenewalBanner(true);
    } else {
      setShowRenewalBanner(false);
    }
  }, [accessResult]);

  return {
    subscriptionStatus,
    hasActiveSubscription,
    showRenewalBanner,
    setShowRenewalBanner,
    refreshSubscriptionStatus,
    isLoading,
    accessResult,
    isTrialExpired: accessResult?.isTrialExpired || false,
    daysUntilExpiry: accessResult?.daysUntilExpiry
  };
};
