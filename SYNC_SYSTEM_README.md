# Sync System Documentation

## Overview

The sync system enables bidirectional synchronization between the Little Spark CMS and the main application. This system allows CMS administrators to:

1. **Sync CMS content to Main App**: Push published challenges and users from CMS to the main application
2. **Import user-created content**: Pull user-created challenges from the main application into CMS for review
3. **Manage user accounts**: Sync CMS users to the main application for seamless access

## Architecture

### Components

1. **CMS Admin Panel**: `/admin/sync` - Web interface for sync operations (admin-only)
2. **CMS API Endpoints**: Handle sync requests with authentication
3. **Main App API Endpoints**: Receive and provide data for sync operations
4. **Role-based Access Control**: Ensures only CMS admins can perform sync operations

### Security Features

- **Admin-only Access**: Only users with `role: 'admin'` can access sync functionality
- **Token-based Authentication**: Secure API communication using JWT tokens
- **Audit Logging**: All admin actions are logged for security tracking
- **Permission Validation**: Multiple layers of permission checking

## Setup Instructions

### Environment Variables

Add these environment variables to both applications:

#### CMS (.env)
```bash
# Main application URL for sync operations
MAIN_APP_BASE_URL=http://localhost:3000

# Secure token for API communication between systems
CMS_SYNC_TOKEN=your-secure-sync-token-here
```

#### Main Application (.env)
```bash
# CMS URL for admin panel links
CMS_ADMIN_URL=http://localhost:3001

# Same secure token as CMS (must match)
CMS_SYNC_TOKEN=your-secure-sync-token-here
```

### Database Schema Updates

The following fields were added to support sync operations:

#### Main App - Profile Table
```sql
ALTER TABLE "profiles" ADD COLUMN "cms_user_id" TEXT;
ALTER TABLE "profiles" ADD COLUMN "is_cms_user" BOOLEAN DEFAULT false;
ALTER TABLE "profiles" ADD COLUMN "cms_role" TEXT;
ALTER TABLE "profiles" ADD COLUMN "cms_specialties" TEXT[] DEFAULT ARRAY[]::TEXT[];
ALTER TABLE "profiles" ADD COLUMN "bio" TEXT;
```

#### CMS - Challenges Collection
```typescript
// Added to Challenges.ts
{
  name: 'mainAppId',
  type: 'text',
  admin: { description: 'ID from main application (for synced challenges)' }
},
{
  name: 'isUserGenerated',
  type: 'checkbox',
  defaultValue: false,
  admin: { description: 'Whether this challenge was created by users in the main app' }
},
{
  name: 'syncStatus',
  type: 'select',
  options: [
    { label: 'Not Synced', value: 'not_synced' },
    { label: 'Synced to Main App', value: 'synced_to_main' },
    { label: 'Synced from Main App', value: 'synced_from_main' },
    { label: 'Sync Failed', value: 'sync_failed' }
  ]
}
```

## API Endpoints

### CMS Endpoints

#### Challenge Sync
- `GET /api/sync/challenges` - Get sync statistics
- `POST /api/sync/challenges` - Sync CMS challenges to main app

#### User Sync  
- `GET /api/sync/users` - Get user sync statistics
- `POST /api/sync/users` - Sync CMS users to main app

#### Import from Main App
- `GET /api/sync/from-main-app` - Check available user-created challenges
- `POST /api/sync/from-main-app` - Import user-created challenges from main app

#### Authentication
- `GET /api/auth/verify` - Verify user authentication and permissions

### Main App Endpoints

#### Sync Receivers
- `POST /api/challenges/sync-from-cms` - Receive synced challenges from CMS
- `POST /api/users/sync-from-cms` - Receive synced users from CMS

#### Data Providers
- `GET /api/challenges/user-created` - Provide user-created challenges for CMS import
- `GET /api/challenges/sync-status` - Provide sync statistics
- `GET /api/users/sync-status` - Provide user sync statistics

## Usage Guide

### For CMS Administrators

1. **Access Sync Panel**
   - Navigate to `/admin/sync` in the CMS
   - Only visible to users with admin role

2. **Sync Challenges to Main App**
   - Click "Sync Challenges to Main App"
   - All published CMS challenges will be synced
   - View sync results and statistics

3. **Sync Users to Main App**
   - Click "Sync Users to Main App" 
   - All active non-admin CMS users will be synced
   - Users get active subscription status in main app

4. **Import User Challenges**
   - Click "Import User Challenges from Main App"
   - User-created challenges are imported for review
   - Imported challenges have status "review"

### For Content Creators

- Content creators cannot access sync functionality
- They can create challenges in CMS normally
- Admins control when content is synced to main app

### For Main App Users

- Users can create challenges in main app
- User-created challenges can be imported to CMS by admins
- CMS users get seamless access to main app features

## Security Considerations

### Access Control
- Sync functionality is completely hidden from non-admin users
- Multiple authentication layers prevent unauthorized access
- Admin actions are logged for audit trails

### Data Integrity
- Sync operations are idempotent (safe to run multiple times)
- Existing data is updated, not duplicated
- Failed syncs are logged and reported

### Token Security
- Use strong, unique tokens for `CMS_SYNC_TOKEN`
- Rotate tokens regularly in production
- Never expose tokens in client-side code

## Troubleshooting

### Common Issues

1. **"Admin access required" error**
   - Ensure user has `role: 'admin'` in CMS
   - Check user is active (`isActive: true`)
   - Verify authentication token is valid

2. **"Unauthorized sync request" error**
   - Check `CMS_SYNC_TOKEN` matches in both applications
   - Ensure token is properly set in environment variables
   - Verify API endpoints are accessible

3. **Sync statistics not loading**
   - Check network connectivity between CMS and main app
   - Verify `MAIN_APP_BASE_URL` is correct
   - Check main app API endpoints are running

4. **Database errors during sync**
   - Ensure database schema updates are applied
   - Check database connectivity
   - Verify required fields are present

### Logs and Monitoring

- Admin actions are logged with format: `🔐 [ADMIN-ACTION]`
- Sync operations are logged with format: `🔄 [CMS-SYNC]` or `🔄 [MAIN-APP-SYNC]`
- Check browser console for client-side errors
- Monitor server logs for API errors

## Development Notes

### Adding New Sync Operations

1. Create API endpoint in CMS (`/api/sync/...`)
2. Add corresponding receiver in main app (`/api/.../sync-from-cms`)
3. Update sync dashboard UI
4. Add JavaScript handlers
5. Test with admin user

### Extending Permissions

1. Update `adminAuth.ts` middleware
2. Add new permission checks
3. Update UI components to respect permissions
4. Test access control thoroughly

## Production Deployment

### Checklist

- [ ] Environment variables configured
- [ ] Database migrations applied
- [ ] Secure tokens generated
- [ ] Admin users created
- [ ] Network connectivity verified
- [ ] SSL certificates configured
- [ ] Monitoring and logging enabled
- [ ] Backup procedures in place

### Monitoring

- Monitor sync operation frequency and success rates
- Track admin user activities
- Alert on sync failures
- Monitor API response times
- Check database performance during sync operations
